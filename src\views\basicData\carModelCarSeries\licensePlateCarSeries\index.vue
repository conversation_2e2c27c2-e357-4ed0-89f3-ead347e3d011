<template>
  <page-wrapper v-show="showCurrentRoute">
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <TableAction :actions="renderTableTitleActions()" />
      </template>
      <template #action="{ record }">
        <TableAction :actions="renderTableActions(record)" />
      </template>
    </BasicTable>
  </page-wrapper>
  <!-- 批量导入 -->
  <BatchImportModal
    @register="registerModal"
    :title="importModalTitle"
    :api="importModalApi"
    :template-fn="getTemplateFn"
    :before-upload="beforeUpload"
    @success="reload"
    @fail="reload"
  />
  <!-- 新增/编辑 品牌/车系 -->
  <BasicModal
    @register="registerEditFormModal"
    :destroy-on-close="true"
    :confirm-loading="confirmLoading"
    :title="editFormModalTitle"
    :maximize="false"
    :maskClosable="false"
    :canFullscreen="false"
    :show-cancel-btn="actionType !== ActionTypeEnum.VIEW"
    :draggable="false"
    @ok="handleSubmit"
  >
    <div class="pt-6 pl-6 pr-6 pb-6">
      <BasicForm :schemas="editFormSchemas" @register="registerForm" />
    </div>
  </BasicModal>
  <EditTableModal
    :min-height="520"
    :tableProps="editTableProps"
    :validateDataSource="validateDataSource"
    :modalTitle="modalTitle"
    :afterDeleteTableDataRecord="afterDeleteTableDataRecord"
    ref="editTableModalRef"
    @register="registerEditTableModal"
    @row-click="handleClickRow"
    @ok="
      handleEditOk(() => {
        onEditOk();
      })
    "
  />
  <router-view />
</template>

<script lang="ts" setup>
  import { useRoute } from 'vue-router';
  import { addSjLogFn } from '/@/api/common/api';
  import PageWrapper from '/@/components/Page/src/PageWrapper.vue';
  import { TableAction, BasicColumn, BasicTable, FormSchema, ActionItem } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { awaitTo } from '@ruqi/utils-admin';
  import { CarAliasEnum } from '/@/enums/basicData';
  import {
    CarBrandType,
    getCarBrandList,
    importBrandCarSeries,
    addCarBrand,
    updateCarBrand,
    addCarSeries,
    updateCarSeries,
    deleteCarSeries,
    deleteCarBrand,
    queryCarSeries,
    queryCarBrand,
    CarSeriesType,
  } from '/@/api/basicData/carModelCarSeries';
  import { ActionTypeEnum, ActionTypeUnion } from '/@/enums/common';
  import { AuthEnum } from '/@/enums/authEnum';
  import { useMessage } from '/@/hooks/web/useMessage';
  import BatchImportModal from '/@/components/BasicData/BatchImportModal.vue';
  import { BasicModal, useModal } from '/@/components/Modal';
  import { computed, onMounted, ref, unref } from 'vue';
  import { useLanguageOptions } from '/@/hooks/common/useLanguageOptions';
  import { getObsTemporaryUrls, ObsTemporaryType } from '/@/api/obs';
  import { useLocale } from '/@/locales/useLocale';
  import { downloadExcelFile } from '/@/utils/file/download';
  import { UploadFile } from 'ant-design-vue';
  import { PageInfoParamsType } from '/@/types/common';
  import { useForm, BasicForm } from '/@/components/Form';
  import { useShowCurrentRouteByName } from '/@/hooks/route';
  import EditTableModal from '../carModelMapping/components/EditTableModal.vue';
  import { useCarAliasConfig } from '../carModelMapping/hooks/useCarAliasConfig';

  const t = useI18n('common').t;

  const { createConfirm, createMessage } = useMessage();

  const route = useRoute();

  const showCurrentRoute = useShowCurrentRouteByName('LicensePlateCarSeries');

  const { languageOptions, getData: getLanguageOptions } = useLanguageOptions();

  const [registerModal, { openModal }] = useModal();

  const [registerEditFormModal, { openModal: openEditFormModal, closeModal: closeEditFormModal }] = useModal();

  const importModalApi = importBrandCarSeries;

  const confirmLoading = ref<boolean>(false);

  const { getLocale } = useLocale();

  const locale = unref(getLocale).replace('-', '_');

  const importModalTitle = t('importBrandCarSeries');

  // 区分 新增/编辑 品牌/车系
  const editFormModalTitle = computed(() => {
    // 提取出操作类型和对象类型映射
    const actionText: Record<string, ActionTypeUnion> = {
      [ActionTypeEnum.ADD]: ActionTypeEnum.ADD,
      [ActionTypeEnum.EDIT]: ActionTypeEnum.EDIT,
      [ActionTypeEnum.VIEW]: ActionTypeEnum.VIEW,
    };
    const objectType = isBrandRecord.value ? 'Brand' : 'CarSeries';
    // 获取当前操作类型，默认为查看
    const action = actionType.value ? actionText[actionType.value] : ActionTypeEnum.VIEW;
    // 动态组合i18n键名
    return t(`${action}${objectType}`);
  });

  const downloadFileName = 'import_car_series_template';

  const actionType = ref<ActionTypeUnion>();

  const isBrandRecord = ref<boolean>(false);

  const disabled = computed(() => actionType.value === ActionTypeEnum.VIEW);

  const disabledCode = computed(() => actionType.value === ActionTypeEnum.EDIT || actionType.value === ActionTypeEnum.VIEW);

  /** 品牌代码是否可编辑 */
  const disabledBrandCode = computed(() => {
    if (!isBrandRecord.value) {
      return true;
    }
    return disabledCode.value;
  });

  const editFormSchemas: FormSchema[] = [
    {
      label: '',
      ifShow: false,
      field: 'id',
      component: 'Input',
    },
    {
      label: t('brandCode'),
      field: 'brandCode',
      component: 'Input',
      required: true,
      colProps: { span: 24 },
      labelWidth: '100%',
      componentProps: {
        placeholder: t('pleaseEnterContent'),
        disabled: disabledBrandCode,
      },
    },
    {
      label: t('brandName'),
      field: 'brandName',
      component: 'Input',
      required: true,
      colProps: { span: 24 },
      labelWidth: '100%',
      componentProps: {
        placeholder: t('pleaseEnterContent'),
        disabled,
      },
      ifShow: () => isBrandRecord.value,
    },
    // 车系代码
    {
      label: t('carSeriesCode'),
      field: 'seriesCode',
      component: 'Input',
      required: true,
      colProps: { span: 24 },
      labelWidth: '100%',
      componentProps: {
        placeholder: t('pleaseEnterContent'),
        disabled: disabledCode,
      },
      ifShow: () => !unref(isBrandRecord),
    },
    // 车系名称
    {
      label: t('carSeriesName'),
      field: 'seriesName',
      component: 'Input',
      required: true,
      colProps: { span: 24 },
      labelWidth: '100%',
      componentProps: {
        placeholder: t('pleaseEnterContent'),
        disabled,
      },
      ifShow: () => !unref(isBrandRecord),
    },
  ];

  const [registerForm, { setFieldsValue, getFieldsValue, validateFields }] = useForm({
    schemas: editFormSchemas,
    autoSubmitOnEnter: false,
    showResetButton: false,
    showCancelButton: false,
    showSubmitButton: false,
    buttonLayout: 'left',
    labelAlign: 'left',
    layout: 'vertical',
    wrapperCol: { span: 24 },
  });

  const {
    registerEditTableModal,
    fetchAliasByType,
    editTableProps,
    validateDataSource,
    handleEditOk,
    modalTitle,
    handleClickRow,
    afterDeleteTableDataRecord,
    editTableModalRef,
  } = useCarAliasConfig();

  const columns: BasicColumn[] = [
    {
      title: t('brandCarSeriesName'),
      dataIndex: 'brandName',
      width: 100,
      resizable: true,
      customRender: ({ record }) => {
        return record.brandName ?? record.seriesName;
      },
    },
    {
      title: t('brandCarSeriesCode'),
      dataIndex: 'brandCode',
      width: 150,
      resizable: true,
      customRender: ({ record }) => {
        return record.brandCode ?? record.seriesCode;
      },
    },
    // 隐藏
    // {
    //   title: t('brandAndCarSeriesDomesticCode'),
    //   dataIndex: 'brandInCountryCode',
    //   width: 150,
    //   resizable: true,
    //   customRender: ({ record }) => {
    //     return record.brandInCountryCode ?? record.seriesInCountryCode;
    //   },
    // },
    // {
    //   title: t('status'),
    //   dataIndex: 'status',
    //   width: 150,
    //   resizable: true,
    //   customRender: ({ record }) => getLabelFromDict(record.status, StatusOptions),
    // },
  ];

  const searchFormSchema: FormSchema[] = [
    {
      label: t('brandName'),
      field: 'brandName',
      component: 'Input',
      labelWidth: '100%',
      componentProps: {
        placeholder: t('pleaseEnterContent'),
      },
    },
    {
      label: t('brandCode'),
      field: 'brandCode',
      labelWidth: '100%',
      component: 'Input',
      componentProps: {
        placeholder: t('pleaseEnterContent'),
      },
    },
    {
      label: t('carSeriesName'),
      field: 'seriesName',
      labelWidth: '100%',
      component: 'Input',
      componentProps: {
        placeholder: t('pleaseEnterContent'),
      },
    },
    {
      label: t('carSeriesCode'),
      field: 'seriesCode',
      labelWidth: '100%',
      component: 'Input',
      componentProps: {
        placeholder: t('pleaseEnterContent'),
      },
    },
  ];

  const fetchCarBrandList = async (params: PageInfoParamsType) => {
    if (!showCurrentRoute) return;
    const [err, content] = await awaitTo(getCarBrandList(params));
    if (!err) {
      const newRecords = (content?.records as unknown as CarBrandType[])?.map((item: CarBrandType) => {
        return {
          ...item,
          aliasType: CarAliasEnum.BRAND,
          carSeries: item.carSeries?.map((series) => ({
            ...series,
            aliasType: CarAliasEnum.SERIES,
          })),
        };
      });
      return newRecords;
    }
    return [];
  };

  const { tableContext, onExportXls } = useListPage({
    designScope: 'LicensePlateCarSeries',
    tableProps: {
      api: fetchCarBrandList,
      columns,
      formConfig: {
        schemas: searchFormSchema,
        buttonLayout: 'right',
        layout: 'vertical',
      },
      useSearchForm: true,
      showTableSetting: false,
      showActionColumn: true,
      actionColumn: {
        title: t('operation'),
        width: 150,
      },
      isTreeTable: true,
      childrenColumnName: 'carSeries',
    },
    exportConfig: {
      url: '/car/brand/export',
      name: `carBrandSeriesList_${locale}`,
      methods: 'post'
    },
  });

  const [registerTable, { reload }] = tableContext;

  const renderTableTitleActions = (): ActionItem[] => {
    return [
      {
        label: t('addBrand'),
        size: 'middle',
        onClick: () => {
          actionType.value = ActionTypeEnum.ADD;
          console.log('actionType', actionType.value);
          isBrandRecord.value = true;
          openEditFormModal();
        },
        auth: AuthEnum.ADD_LICENSE_PLATE_CAR_SERIES,
        type: 'primary',
      },
      {
        label: t('import'),
        size: 'middle',
        onClick: () => {
          openModal();
        },
        type: 'primary',
        auth: AuthEnum.IMPORT_LICENSE_PLATE_CAR_SERIES,
      },
      {
        label: t('export'),
        size: 'middle',
        onClick: () => {
          onExportXls();
        },
        type: 'primary',
        auth: AuthEnum.EXPORT_LICENSE_PLATE_CAR_SERIES,
      },
    ];
  };

  /** 打开编辑表单 */
  const handleOpenEditFormModal = (record: Recordable, type: ActionTypeUnion) => {
    isBrandRecord.value = record.aliasType === CarAliasEnum.BRAND;
    actionType.value = type;
    openEditFormModal();
    getDetail(record.id);
  };

  const renderTableActions = (record: Recordable) => {
    const isBrand = record.aliasType === CarAliasEnum.BRAND;

    const actions: ActionItem[] = [
      {
        label: t('view'),
        onClick: () => {
          handleOpenEditFormModal(record, ActionTypeEnum.VIEW);
        },
        auth: AuthEnum.VIEW_LICENSE_PLATE_CAR_SERIES,
      },
      {
        label: t('edit'),
        onClick: () => {
          handleOpenEditFormModal(record, ActionTypeEnum.EDIT);
        },
        auth: AuthEnum.EDIT_LICENSE_PLATE_CAR_SERIES,
      },
      {
        label: t('delText'),
        color: 'error',
        onClick: async () => {
          if (isBrand && record.carSeries?.length > 0) {
            // 若存在下级关联数据，则提示 存在关联数据无法删除
            createMessage.error(t('existAssociatedDataCannotBeDeleted'));
            return;
          }
          actionType.value = ActionTypeEnum.DELETE;
          isBrandRecord.value = isBrand;
          createConfirm({
            title: t('okTextDelete'),
            iconType: 'warning',
            content: `${t('deleteTip')}`,
            okText: t('okText'),
            cancelText: t('cancelText'),
            onOk: async () => {
              const [err] = await awaitTo(getApiMap().actions[actionType.value!]({ id: record.id }));
              if (!err) {
                createMessage.success(t('operationSuccess'));
                reload();
              }
            },
          });
        },
        auth: AuthEnum.DELETE_LICENSE_PLATE_CAR_SERIES,
      },
      {
        label: t('setAlias'),
        onClick: () => {
          fetchAliasByType(record.aliasType, isBrand ? record.brandCode : record.seriesCode);
        },
        auth: isBrand ? AuthEnum.SET_BRAND_ALIAS : AuthEnum.SET_CAR_SERIES_ALIAS,
      },
    ];

    if (isBrand) {
      // 新增车系
      actions.push({
        label: t('addCarSeries'),
        onClick: () => {
          handleOpenEditFormModal(record, ActionTypeEnum.ADD);
          isBrandRecord.value = false;
        },
        auth: AuthEnum.ADD_LICENSE_PLATE_CAR_SERIES,
      });
    }

    return actions;
  };

  const getTemplateFn = async () => {
    const [err, result] = await awaitTo(
      getObsTemporaryUrls({
        type: ObsTemporaryType.Download,
        filePath: `public/${downloadFileName}_${locale}.xlsx`,
      })
    );
    if (err) {
      createMessage.error(t('file_download_fail'));
      return;
    }
    if (result.content) {
      try {
        await downloadExcelFile(result.content, `${downloadFileName}_${locale}.xlsx`);
      } catch (err) {
        createMessage.error(t('file_download_fail'));
      }
    }
  };

  /** 上传前处理，判断文件名是否符合模版格式 */
  const beforeUpload = (file: UploadFile) => {
    const fileName = file.name;

    let result = false;

    const hasLocale = languageOptions.value.some((item) => (item.value as string).replace('-', '_') === locale);

    if (hasLocale) {
      result = fileName.includes(downloadFileName);
    }

    if (!result) {
      createMessage.error(t('pleaseSelectTheCorrectTemplateFile'));
    }

    return result;
  };

  const getApiMap = <T,>() => {
    // 区分品牌和车系
    if (unref(isBrandRecord)) {
      return {
        query: queryCarBrand as (data: { id: number }) => Promise<T>,
        actions: {
          [ActionTypeEnum.ADD]: addCarBrand,
          [ActionTypeEnum.EDIT]: updateCarBrand,
          [ActionTypeEnum.VIEW]: queryCarBrand,
          [ActionTypeEnum.DELETE]: deleteCarBrand,
        },
      };
    }
    return {
      query: queryCarSeries as (data: { id: number }) => Promise<T>,
      actions: {
        [ActionTypeEnum.ADD]: addCarSeries,
        [ActionTypeEnum.EDIT]: updateCarSeries,
        [ActionTypeEnum.VIEW]: queryCarSeries,
        [ActionTypeEnum.DELETE]: deleteCarSeries,
      },
    };
  };

  const handleSubmit = async () => {
    if (actionType.value === ActionTypeEnum.VIEW) {
      closeEditFormModal();
      return;
    }
    try {
      await validateFields();
      confirmLoading.value = true;
      const fieldsValue = getFieldsValue();
      const params = {
        ...fieldsValue,
      };
      // 只有新增时，删除id
      if (actionType.value === ActionTypeEnum.ADD) {
        delete params.id;
      }
      const api = getApiMap().actions[actionType.value!];
      // return;
      const [err] = await awaitTo(api(params));
      if (!err) {
        createMessage.success(t('operationSuccess'));
        reload();
        closeEditFormModal();
      }
    } catch (err) {
      console.log('handleSubmit', err);
    } finally {
      confirmLoading.value = false;
    }
  };

  /** 获取详情 */
  const getDetail = async (id: number) => {
    if (actionType.value === ActionTypeEnum.ADD && !unref(isBrandRecord)) {
      return;
    }
    const api = getApiMap<CarBrandType | CarSeriesType>().query;
    const [err, result] = await awaitTo(api({ id }));
    if (!err) {
      setFieldsValue(result);
    }
  };

  onMounted(async () => {
    await awaitTo(getLanguageOptions());
  });

  const onEditOk = () => {
    // 审计日志当前操作页面-菜单
    const remarkLogStr = JSON.stringify(route?.meta?.originalTitles || []);
    addSjLogFn(remarkLogStr, '设置别名');
  };
</script>
