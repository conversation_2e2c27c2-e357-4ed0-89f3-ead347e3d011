<!-- 
  Cron组件
  用于配置定时任务触发规则，支持多种重复频率配置

  核心功能：
  1. 支持"不重复"和"自定义重复"两种模式
  2. 支持按天/周/月/年四种重复规则
  3. 自动计算首次触发时间和触发规则描述
  4. 生成标准Cron表达式
  5. 兼容国际化
  6. 支持显示触发规则验证
  7. 时区转换，支持用户系统时区和服务器时区 UTC+8 转换

  日期计算逻辑优化：
  - 使用 calculateTriggerDateByRule 核心函数统一处理日期计算
  - 支持三种计算类型：下次触发时间(nextTrigger)、最小结束时间(minEnd，等同于nextTrigger)和规则验证(validation)
  - 减少了重复代码，提高了可维护性
  - 支持时区转换，确保计算结果符合预期
-->

<template>
  <div class="schedule-config">
    <a-form layout="vertical" :model="formState">
      <!-- 开始时间 -->
      <a-row :gutter="gutter">
        <a-col :span="formItemSpan">
          <a-form-item
            :label="timeRangeLabel?.[0]"
            name="startTime"
            :rules="[{ required: true, message: t('pleaseSelectStartTime') }]"
            :placeholder="t('pleaseSelectStartTime')"
          >
            <a-date-picker
              v-model:value="formState.startTime"
              :show-time="{ format: 'HH:mm' }"
              :format="BASE_DAY_JS_FORMAT"
              style="width: 100%"
              :disabled-date="disabledPastDate"
              @change="handleStartTimeChange"
              :showNow="false"
              :allowClear="false"
              :disabled="disabled"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 重复频率选择 -->
      <a-row :gutter="gutter">
        <a-col :span="formItemSpan">
          <a-form-item :label="t('repetitionFrequency')" name="repeatType" :rules="[{ required: true, message: t('chooseText') }]">
            <a-select v-model:value="formState.repeatType" @change="handleRepeatTypeChange" :disabled="disabled">
              <a-select-option v-for="option in REPEAT_OPTIONS" :key="option.value" :value="option.value">
                {{ option.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 重复规则相关配置，只在选择"重复"时显示 -->
      <template v-if="formState.repeatType === 'repeat'">
        <!-- 重复规则配置 -->
        <a-row :gutter="gutter">
          <a-col :span="formItemSpan">
            <a-form-item :label="t('repeatRules')">
              <a-space>
                <span>{{ t('every') }}</span>
                <a-form-item-rest>
                  <a-select
                    v-model:value="formState.interval"
                    style="width: 80px"
                    :status="intervalError ? 'error' : ''"
                    :disabled="disabled || formState.repeatRuleType === 'week'"
                  >
                    <a-select-option v-for="n in 30" :key="n" :value="n">{{ n }}</a-select-option>
                  </a-select>
                </a-form-item-rest>
                <a-form-item-rest>
                  <a-select
                    v-model:value="formState.repeatRuleType"
                    @change="handleRuleTypeChange"
                    style="width: 100px"
                    :status="ruleTypeError ? 'error' : ''"
                    :disabled="disabled"
                  >
                    <a-select-option v-for="type in REPEAT_RULE_TYPES" :key="type.value" :value="type.value">
                      {{ type.label }}
                    </a-select-option>
                  </a-select>
                </a-form-item-rest>
              </a-space>
              <div v-if="intervalError || ruleTypeError" class="ant-form-item-explain ant-form-item-explain-error">
                <div role="alert">{{ t('pleaseConfigureRepeatRules') }}</div>
              </div>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 月份日期选择，仅在"月"规则下显示 -->
        <a-row :gutter="gutter" v-if="formState.repeatRuleType === 'month'">
          <a-col :span="formItemSpan">
            <a-form-item name="monthDays">
              <div class="date-button-grid">
                <a-tooltip
                  v-for="day in monthDayRange"
                  :key="day"
                  :title="formState.monthDays.length === 1 && formState.monthDays.includes(day) ? t('pleaseSelectAtLeastOneDate') : null"
                >
                  <a-button
                    :type="formState.monthDays.includes(day) ? 'primary' : 'default'"
                    :class="{ 'date-button-selected': formState.monthDays.includes(day) }"
                    :disabled="(formState.monthDays.length === 1 && formState.monthDays.includes(day)) || disabled"
                    class="date-button"
                    @click="toggleMonthDay(day)"
                  >
                    {{ day }}
                  </a-button>
                </a-tooltip>
              </div>
              <div v-if="formState.monthDays.length === 0" class="ant-form-item-explain ant-form-item-explain-error">
                <div role="alert">{{ t('pleaseSelectAtLeastOneDate') }}</div>
              </div>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 周几选择，仅在"周"规则下显示 -->
        <a-row :gutter="gutter" v-if="formState.repeatRuleType === 'week'">
          <a-col :span="formItemSpan">
            <a-form-item :label="t('week')" name="weekDays" :rules="[{ required: true, message: t('pleaseSelectWeek'), type: 'array', min: 1 }]">
              <a-select v-model:value="formState.weekDays" mode="multiple" style="width: 100%" :disabled="disabled">
                <a-select-option v-for="(day, index) in weekDaysOptions" :key="index + 1" :value="index + 1">
                  {{ day }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 结束时间 -->
        <a-row :gutter="gutter" v-if="formState.repeatType === REPEAT_TYPE_ENUM.REPEAT">
          <a-col :span="formItemSpan">
            <a-form-item :label="timeRangeLabel?.[1]" name="endTime" :rules="[{ required: true, message: '' }, { validator: validateEndTime }]">
              <!-- :disabled-date="disabledEndDate"  暂时注释-->
              <a-date-picker
                v-model:value="formState.endTime"
                :show-time="{ format: 'HH:mm' }"
                :format="BASE_DAY_JS_FORMAT"
                :disabled-date="disabledEndDate"
                style="width: 100%"
                :showNow="false"
                :close-calendar="true"
                :allowClear="false"
                :disabled="disabled"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 规则描述展示 -->
        <a-row :gutter="gutter">
          <a-col :span="formItemSpan">
            <a-form-item :label="t('triggerRuleDescription')">
              <a-card class="rule-description-card" :class="{ 'rule-error-card': !triggerRuleValidation.isValid && showTriggerRuleValidation }">
                <div class="rule-description">
                  <div class="rule-title">{{ `${t('rule')}: ${ruleDescription}` }}</div>
                  <template v-if="showTriggerRuleValidation">
                    <div v-if="triggerRuleValidation.isValid" class="next-trigger-time">
                      <span>{{ `${t('firstTriggerTime')}: ${nextTriggerTime}` }}</span>
                    </div>
                    <div v-else class="trigger-rule-error">
                      <span>{{ triggerRuleValidation.message }}</span>
                    </div>
                  </template>
                </div>
              </a-card>
            </a-form-item>
          </a-col>
        </a-row>
      </template>

      <!-- 提交按钮 -->
      <a-row :gutter="gutter" v-if="showSubmitButton">
        <a-col :span="formItemSpan">
          <a-form-item>
            <a-button type="primary" @click="handleSubmit" :disabled="disabled">{{ t('saveText') }}</a-button>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </div>
</template>

<script setup>
  import { watch, onBeforeUnmount } from 'vue';
  import { message } from 'ant-design-vue';
  import dayjs from 'dayjs';
  import { REPEAT_TYPE_ENUM } from '/@/enums/common';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useCronForm, useCronExpression, useCronRuleDescription, cronDateUtils } from './hooks';
  import { BASE_DAY_JS_FORMAT, BASE_DAY_JS_FORMAT_WITH_SECONDS } from './hooks/useCronForm';

  const { t } = useI18n('common');

  const props = defineProps({
    showSubmitButton: {
      type: Boolean,
      default: true,
    },
    formItemSpan: {
      type: Number,
      default: 24,
    },
    gutter: {
      type: Number,
      default: 16,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    // 定时任务的结束时间，只对重复任务有效
    endTime: {
      type: String,
      default: '',
    },
    // 传入的cron表达式
    cronExpression: {
      type: String,
      // 比如：
      // 0 32 18 1/30 * ? * 表示每30天执行一次，第一次执行时间是18:32:00
      // 0 48 17 5 6 ? 2025 表示2025年6月5日17:48:00执行一次
      default: '',
    },
    // 定时任务的开始时间，用于解决时区计算问题
    originalStartTime: {
      type: String,
      default: '',
    },
    /** 是否显示触发规则验证，默认显示 */
    showTriggerRuleValidation: {
      type: Boolean,
      default: true,
    },
  });

  // 使用hooks
  const {
    formState,
    REPEAT_OPTIONS,
    REPEAT_RULE_TYPES,
    monthDayRange,
    intervalError,
    ruleTypeError,
    weekDaysOptions,
    timeRangeLabel,
    disabledPastDate,
    disabledEndDate,
    handleStartTimeChange,
    handleRepeatTypeChange,
    handleRuleTypeChange,
    toggleMonthDay,
    nextTriggerTime,
    validate,
    datePickerLocale,
  } = useCronForm(props);

  const { computedCronExpression, checkTriggerRule, triggerRuleValidation, calculateMinEndTime, parseCronExpression } = useCronExpression(
    formState,
    t,
    props
  );

  const { ruleDescription } = useCronRuleDescription(formState, t, weekDaysOptions, datePickerLocale);

  // 表单提交
  const handleSubmit = async () => {
    // 准备提交数据
    const submitData = {
      cronExpression: '',
      cronEndTime: '',
      cronOriginalStartTime: '', // 保存用户原始选择的开始时间
    };

    try {
      await validate();

      // 检查开始时间是否大于当前时间
      const now = dayjs();
      if (formState.startTime.isBefore(now.minute(now.minute() + 1).second(0))) {
        message.error(t('startTimeMustBeGreaterThanCurrentTime'));
        return;
      }
      // 表单验证通过后进行额外校验
      if (formState.repeatType === REPEAT_TYPE_ENUM.REPEAT) {
        // 检查选中的日期是否符合规则
        if (formState.repeatRuleType === 'week' && formState.weekDays.length === 0) {
          message.error(t('pleaseSelectWeek'));
          return;
        }

        // 年规则不再检查月份和星期
        if (formState.repeatRuleType === 'month' && formState.monthDays.length === 0) {
          message.error(datePickerLocale?.lang?.monthPlaceholder);
          return;
        }

        // 检查结束日期是否达到最低重复要求
        const minEndTime = calculateMinEndTime();
        if (formState.endTime.isBefore(minEndTime)) {
          // 结束时间设置过短，无法满足重复要求
          message.error(t('endTimeError'));
          return;
        }

        // 检查触发规则
        const ruleCheck = checkTriggerRule();
        if (!ruleCheck.isValid) {
          message.error(ruleCheck.message);
          return;
        }
      }

      // 获取用户系统时区
      const userSystemTimezone = cronDateUtils.getUserSystemTimezone();

      // 直接获取计算出的表达式值，无需修改formState
      submitData.cronExpression = computedCronExpression.value;

      // 保存用户原始选择的开始时间（服务器时区）- 将秒设置为0
      const serverStartTime = cronDateUtils.convertDateBetweenTimezones(
        formState.startTime.second(0),
        userSystemTimezone,
        cronDateUtils.SERVER_TIMEZONE
      );
      submitData.cronOriginalStartTime = serverStartTime.format(BASE_DAY_JS_FORMAT_WITH_SECONDS) || '';

      // 转换结束时间
      if (formState.repeatType === REPEAT_TYPE_ENUM.NO_REPEAT) {
        // 如果是一次性任务，结束时间等于开始时间（秒设置为0）
        submitData.cronEndTime = formState.startTime.second(0).format(BASE_DAY_JS_FORMAT_WITH_SECONDS) || '';
      } else if (formState.endTime) {
        // 重复任务使用设定的结束时间（秒设置为0）
        const serverEndTime = cronDateUtils.convertDateBetweenTimezones(
          formState.endTime.second(0),
          userSystemTimezone,
          cronDateUtils.SERVER_TIMEZONE
        );
        submitData.cronEndTime = serverEndTime.format(BASE_DAY_JS_FORMAT_WITH_SECONDS) || '';
      }
    } catch (error) {
      console.error('验证失败', error);
      return;
    }
    return submitData;
  };

  // 监听cronExpression属性变化
  const watchCronExpression = watch(
    () => props.cronExpression,
    (newValue) => {
      if (newValue) {
        parseCronExpression(newValue);
      }
    },
    { immediate: true }
  );

  onBeforeUnmount(() => {
    watchCronExpression();
  });

  // 校验结束时间 - 为了保持组件兼容性
  const validateEndTime = async (rule, value) => {
    if (!value) return Promise.reject(t('pleaseSelectEndTime'));

    if (!formState.startTime) {
      return Promise.reject(t('pleaseSelectStartTime'));
    }

    if (value.isBefore(formState.startTime)) {
      return Promise.reject(t('endTimeMustBeGreaterThanStartTime'));
    }

    return Promise.resolve();
  };

  // 对外暴露提交方法
  defineExpose({
    handleSubmit,
  });
</script>

<style scoped>
  .schedule-config {
    max-width: 600px;
    /* margin: 0 auto; */
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  }

  .rule-description-card {
    background-color: #f5f5f5;
    border: none;
    transition: all 0.3s;
  }

  .rule-error-card {
    background-color: #fff2f0;
    border: 1px solid #ffccc7;
  }

  .rule-description {
    padding: 10px;
  }

  .rule-title {
    font-weight: 500;
    margin-bottom: 10px;
  }

  .next-trigger-time {
    color: #666;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .trigger-rule-error {
    color: #ff4d4f;
    font-size: 14px;
    margin-top: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .date-button-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 8px;
  }

  .date-button {
    width: 40px;
    height: 40px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    margin: 0;
    padding: 0;
    transition: all 0.3s;
  }

  .date-button:hover:not(:disabled) {
    color: #1890ff;
    border-color: #1890ff;
  }

  .date-button-selected {
    background-color: #1890ff;
    color: #fff;
    border-color: #1890ff;
  }

  .date-button-selected:hover:not(:disabled) {
    background-color: #40a9ff;
    color: #fff;
    border-color: #40a9ff;
  }
</style>
