<template>
  <div :class="['p-15px']" class="user_handle">
    <div :class="['p-15px', prefixCls]">
      <BasicForm @register="registerForm" @submit="handleSubmit" @cancel="handleCancel" @field-value-change="fieldValueChange">
        <!-- 手机号 -->
        <template #phoneSlot="{ model }">
          <div class="flex">
            <a-select
              v-if="pageType === PageTypeEnum.ADD"
              style="width: 120px"
              v-model:value="quhaoVal"
              :showSearch="true"
              :placeholder="t('common.global_roaming')"
              :options="quhaoOptions"
            />
            <a-input class="fix-auto-fill" :placeholder="t('common.mobilePlaceholder')" v-model:value="model.mobileNotQuhao" />
          </div>
        </template>
      </BasicForm>
      <div v-show="pageType === PageTypeEnum.VIEW">
        <a-divider orientation="left">{{ t('common.user_role_text') }}</a-divider>
        <a-row :gutter="20">
          <a-col class="mt-20px" :xxl="12" :xl="12" :lg="12" :md="12" :sm="24" :xs="24" v-for="items in menuTreeList" :key="items.id">
            <BasicTree
              :ref="(el) => handleSetTreeMap(el, items.id)"
              checkable
              :treeData="items.treeData"
              :checkedKeys="items.checkedKeys"
              :checkStrictly="true"
              :expandedKeys="items.expandedKeys"
              :selectedKeys="items.selectedKeys"
              :clickRowToExpand="false"
              :title="items.title"
              :fieldNames="{ title: 'text', key: 'id', children: 'children' }"
            >
              <template #title="{ text }">
                {{ text }}
              </template>
            </BasicTree>
          </a-col>
        </a-row>
        <div class="mt-20px">
          <BasicTable @register="registerTable" />
          <a-button class="mt-20px" @click="handleCancel">{{ t('common.back') }}</a-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  import { BasicTable } from '/@/components/Table';
  import { BasicTree } from '/@/components/Tree';
  import { useTabs } from '/@/hooks/web/useTabs';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { useRoutePageType } from '/@/hooks/route';
  import { PageTypeEnum } from '/@/enums/common';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { submitFormSchema } from './user.data';
  import { getLogInfoApi } from '/@/api/common/api';
  import { queryRegionCountryApi, queryRegionDetailsApi } from '/@/views/system/depart/depart.api';
  import { queryAreaListApi } from '/@/api/common/api';
  import { getBSideUserAllDataList } from '/@/api/basicData/bSideUsers';
  import { getNetworkAllDataList } from '/@/api/basicData/network';
  import { queryDepartTreeApi, querySubCompaniesApi } from '/@/views/system/depart/depart.api';
  import { queryUserInfoApi, queryCountryByUserApi, saveUserInfoApi, updateUserInfoApi } from './user.api';
  import { useI18n } from '/@/hooks/web/useI18n';

  const { t } = useI18n('');
  let router = useRouter();

  const { close: closeTab } = useTabs();

  const pageType = useRoutePageType();

  // 当前编辑、查看的用户信息
  const editOrViewUserInfo = ref();

  // 分配的国家数据
  const countryCodeList = ref([] as any);
  async function getCountryList(userId: string) {
    try {
      const res = await queryCountryByUserApi({
        userId: userId,
      });
      countryCodeList.value = res?.map((mapItems) => {
        return mapItems.countryCode;
      });
      console.log(countryCodeList.value);
    } catch (err) {}
  }

  // 区号数据
  const quhaoOptions = ref([]);
  const quhaoVal = ref(null as any);

  // 获取国际区号
  async function queryAreaListFn() {
    try {
      const res = await queryAreaListApi();
      quhaoOptions.value =
        res?.map((mapItems) => {
          return {
            label: mapItems.areaCode,
            value: mapItems.areaCode,
          };
        }) || [];
    } catch (err) {}
  }

  // 编辑/查看ID
  const userId: any = router.currentRoute.value.query.userId;

  const prefixCls = ref('depart-tree-handle');

  // 大区以及国家数据
  const regionCountryList = ref([] as any);
  // 大区以及国家数据-区域
  const regionCountryQuyuList = ref([] as any);
  async function getRegionCountryList() {
    try {
      const res = await queryRegionCountryApi();
      // 国家大区-country字段所需使用的options
      regionCountryList.value =
        res?.map((mapItems) => {
          return {
            label: mapItems.region,
            value: mapItems.regionCode,
            children:
              mapItems.countries?.map((mapItem) => {
                return {
                  label: mapItem.regionCountry,
                  value: mapItem.regionCountryCode,
                };
              }) || undefined,
          };
        }) || [];
    } catch (err) {}

    try {
      const res = await queryRegionDetailsApi();
      // 国家大区-country字段所需使用的options
      regionCountryQuyuList.value =
        res?.map((mapItems) => {
          return {
            label: mapItems.regionName,
            value: mapItems.regionCode,
            children:
              mapItems.countries?.map((countriesItem) => {
                // 第三级可能存在省也可能存在城市
                let secondData = [] as any;
                if (countriesItem.hasProvince) {
                  secondData = countriesItem.provinces?.map((provincesItems) => {
                    return {
                      label: provincesItems.provinceName,
                      value: `${countriesItem.countryCode}&&&&country____${provincesItems.provinceCode}&&&&provinceLetterCode`,
                      children: provincesItems.cities?.map((citiesItems) => {
                        return {
                          label: citiesItems.cityName,
                          value: `${countriesItem.countryCode}&&&&country____${provincesItems.provinceCode}&&&&provinceLetterCode____${citiesItems.cityCode}&&&&cityLetterCode`,
                          children: null,
                        };
                      }),
                    };
                  });
                } else {
                  secondData = countriesItem.cities?.map((citiesItems) => {
                    return {
                      label: citiesItems.cityName,
                      value: `${countriesItem.countryCode}&&&&country____${citiesItems.cityCode}&&&&cityLetterCode`,
                      children: null,
                    };
                  });
                }
                return {
                  label: countriesItem.countryName,
                  value: `${countriesItem.countryCode}&&&&country`,
                  children: secondData?.length > 0 ? secondData : undefined,
                };
              }) || undefined,
          };
        }) || [];
    } catch (err) {}
  }

  // 初始化b端用户和网点下拉数据
  async function initBuserAndNetworkInfo(countryCode: string) {
    let buserList: any = [];
    let networkList: any = [];
    if (!countryCode) {
      return {
        buserList,
        networkList,
      };
    }
    try {
      const res = await getNetworkAllDataList({
        countryCode,
      });
      networkList =
        res?.map((mapItems) => {
          return {
            label: mapItems.networkName,
            value: mapItems.networkCode,
          };
        }) || [];
    } catch (err) {}
    try {
      const res = await getBSideUserAllDataList({
        countryCode,
      });
      buserList =
        res?.map((mapItems) => {
          return {
            label: mapItems.customerName,
            value: mapItems.customerCode,
          };
        }) || [];
    } catch (err) {}
    return {
      buserList,
      networkList,
    };
  }

  // 按钮提交配置
  const submitButtonOptions = ref({ text: t('common.saveText'), preIcon: '', loading: false });

  const { createMessage } = useMessage();

  const { success } = createMessage;

  // 多个菜单树
  // 所有的选中的菜单ID
  const selectMenuIds = ref([] as any);
  const menuTreeList = ref([] as any);
  const treeRefMap = ref({});
  const handleSetTreeMap = (el, item: string) => {
    if (el) {
      treeRefMap.value[`tree_Ref_${item}`] = el;
    }
  };
  // 递归查询到菜单树的所有ID达到默认展开效果
  function getTreeDisableds(treeData: any) {
    treeData.forEach((item: any) => {
      item.disabled = true;
      if (item.children) {
        getTreeDisableds(item.children);
      }
    });
    return treeData;
  }

  // 组织结构树
  function buildDepartTree(data, key = 'pkFatherCorp', val = '0') {
    let tree: any = [];
    for (const item of data) {
      item[key] = !!item[key] ? item[key] : '0';
      if (item[key] === val) {
        const children = buildDepartTree(data, key, item.code);
        if (children.length) item.children = children;
        tree.push({
          ...item,
          label: item.name,
          value: item.code,
        });
      }
    }
    return tree;
  }

  // 获取公司组织树并重构表单项
  async function getCompanyTreeFn() {
    let companyInfoListData = [] as any;
    try {
      const res1 = await queryDepartTreeApi({});
      const { companyInfoList } = res1;
      companyInfoListData = buildDepartTree(companyInfoList || [], 'pkFatherCorp');
      updateSchema([
        {
          field: 'companyId',
          componentProps: {
            treeData: companyInfoListData,
          },
        },
        {
          field: 'deptId',
          componentProps: {
            treeData: [],
          },
        },
      ]);
    } catch (err) {}
  }

  // 根据公司组织树获取部门组织树
  const getDepartTreeFn = async (value: any) => {
    try {
      const companyInfoListData = await querySubCompaniesApi({
        code: value,
        sourceType: 1,
      });
      const returnData = buildDepartTree(companyInfoListData.filter((filterItems) => filterItems.sourceType === 2) || [], 'pkFatheDept');
      return returnData;
    } catch (err) {
      return [];
    }
  };

  // 列表页面公共参数、方法
  const { tableContext } = useListPage({
    tableProps: {
      api: getLogInfoApi,
      title: t('common.operationLog'),
      columns: [
        {
          title: t('common.operationType'),
          dataIndex: 'logType',
          width: 68,
          align: 'left',
          fixed: 'left',
        },
        {
          title: t('common.operator'),
          dataIndex: 'cnName',
          width: 98,
          align: 'left',
          fixed: 'left',
        },
        {
          title: t('common.operatorAccount'),
          dataIndex: 'enName',
          width: 98,
          align: 'left',
        },
        {
          title: t('common.operationTime'),
          dataIndex: 'createTime',
          width: 130,
          align: 'left',
        },
      ],
      size: 'small',
      pagination: false,
      useSearchForm: false,
      showTableSetting: false,
      showActionColumn: false,
      immediate: false,
      bordered: true,
      showIndexColumn: false,
      listField: 'list',
      beforeFetch: (params) => {
        params.bizId = userId;
        params.bizType = '2';
        return params;
      },
    },
  });
  // 注册table数据
  const [registerTable, { reload }] = tableContext;

  // 表单配置
  const [registerForm, { setProps, setFieldsValue, updateSchema, resetFields, validate }] = useForm({
    schemas: submitFormSchema,
    baseColProps: { span: 12 },
    labelWidth: 120,
    autoSubmitOnEnter: true,
    showResetButton: false,
    showCancelButton: pageType === PageTypeEnum.VIEW ? false : true,
    cancelButtonOptions: { text: t(`${pageType === PageTypeEnum.VIEW ? 'common.back' : 'common.cancelText'}`), preIcon: '' },
    showSubmitButton: [PageTypeEnum.EDIT, PageTypeEnum.ADD].some((v) => v === pageType),
    submitButtonOptions: submitButtonOptions.value,
    buttonLayout: 'left',
    labelAlign: 'left',
    layout: 'vertical',
  });

  /**
   * 表单项改变
   * key: 改变的表单项key
   * value: 改变的表单项值
   */
  const fieldValueChange = async (key: string, value: any) => {
    // 上级组织名称改变-映射上级组织赋值-并重置上级部门名称值和options
    if (key === 'companyId') {
      setFieldsValue({
        companyCode: value,
        deptId: undefined,
        deptCode: undefined,
      });
      const newData = await getDepartTreeFn(value);
      updateSchema({
        field: 'deptId',
        componentProps: {
          treeData: newData,
        },
      });
    }
    // 上级部门名称改变-上级部门编码赋值
    if (key === 'deptId') {
      setFieldsValue({
        deptCode: value,
      });
    }
    // 账户类别改变
    if (key === 'accountCategory') {
      await setFieldsValue({
        regionCode: undefined,
        country: undefined,
        country_area_code: undefined,
        networkCode: undefined,
        networkCode1: undefined,
      });
      await updateSchema([
        {
          field: 'country',
          ifShow: ['2', '3', '', undefined].includes(value),
          componentProps: {
            options: [],
          },
        },
        {
          field: 'country_area_code',
          ifShow: ['1'].includes(value),
          componentProps: {
            options: [],
          },
        },
        {
          field: 'networkCode',
          ifShow: ['1', '2', '', undefined].includes(value),
          componentProps: {
            options: [],
          },
        },
        {
          field: 'networkCode1',
          ifShow: ['3'].includes(value),
          componentProps: {
            options: [],
          },
        },
      ]);
    }
    // 所属大区改变清空国家、B端用户和网点并获取国家下拉数据+B端用户和网点下拉数据赋值空数组
    if (key === 'regionCode') {
      await setFieldsValue({
        country: undefined,
        country_area_code: undefined,
        networkCode: undefined,
        networkCode1: undefined,
      });
      // 获取网点信息下拉数据
      await updateSchema([
        {
          field: 'country',
          componentProps: {
            options: !value ? [] : regionCountryList.value.find((findItems) => findItems.value === value)?.children || [],
          },
        },
        {
          field: 'country_area_code',
          componentProps: {
            treeData: !value ? [] : regionCountryQuyuList.value.find((findItems) => findItems.value === value)?.children || [],
          },
        },
        {
          field: 'networkCode',
          componentProps: {
            options: [],
          },
        },
        {
          field: 'networkCode1',
          componentProps: {
            options: [],
          },
        },
      ]);
    }
    // 所属国家改变清空B端用户和网点并获取B端用户和网点数据
    if (key === 'country' || key === 'country_area_code') {
      await setFieldsValue({
        networkCode: undefined,
        networkCode1: undefined,
      });
      let thisValue = '';
      if (key === 'country_area_code') {
        if (!!value) {
          thisValue = value.split('____')?.[0]?.split('&&&&')[0];
        }
      } else {
        thisValue = value;
      }
      const { buserList, networkList } = await initBuserAndNetworkInfo(thisValue);
      // 获取网点信息下拉数据
      await updateSchema([
        {
          field: 'networkCode',
          componentProps: {
            options: buserList,
          },
        },
        {
          field: 'networkCode1',
          componentProps: {
            options: networkList,
          },
        },
      ]);
    }
  };

  // 表单提交
  const handleSubmit = async () => {
    let params: any = {};
    try {
      const values = await validate();
      params = values;
      if (pageType !== PageTypeEnum.ADD && !params.mobile && !params.email) {
        return createMessage.warning(t('common.phoneAndEmail'));
      }
      if (pageType === PageTypeEnum.ADD && !params.mobileNotQuhao && !params.email) {
        return createMessage.warning(t('common.phoneAndEmail'));
      }
      if (pageType === PageTypeEnum.ADD) {
        if (!!params.mobileNotQuhao && !quhaoVal.value) {
          return createMessage.warn(`${t('common.chooseText')}${t('common.global_roaming')}`);
        }
      }
    } catch (err) {
      return createMessage.warning(t('common.please_enter_required_fields'));
    }
    // 账户类别
    // 账号类别='1'，即厂端
    if (params.accountCategory === '1') {
      if (!params.regionCode) {
        return createMessage.warning(t('common.please_select_region_text'));
      }
    }
    // 账号类别='2'，即选择总代&子公司
    if (params.accountCategory === '2') {
      if (!params.regionCode) {
        return createMessage.warning(t('common.please_select_region_text'));
      }
      if (!params.country) {
        return createMessage.warning(t('common.please_select_country_text'));
      }
      if (!params.networkCode) {
        return createMessage.warning(t('common.please_select_bSideUsers'));
      }
    }
    // 账号类别='3'，即选择网点
    if (params.accountCategory === '3') {
      if (!params.regionCode) {
        return createMessage.warning(t('common.please_select_region_text'));
      }
      if (!params.country) {
        return createMessage.warning(t('common.please_select_country_text'));
      }
      if (!params.networkCode1) {
        return createMessage.warning(t('common.please_select_network_text'));
      }
    }
    submitButtonOptions.value.loading = true;
    const api = pageType === PageTypeEnum.ADD ? saveUserInfoApi : updateUserInfoApi;
    try {
      for (let i in params) {
        if ((params[i] ?? '') === '') {
          params[i] = '';
        }
      }
      // 国家/省/市数据回显
      let submitAreaParams: any = {};
      if (['1'].includes(params.accountCategory)) {
        const newData = params.country_area_code.split('____');
        for (let i = 0; i < newData.length; i++) {
          const [val, key] = newData[i].split('&&&&');
          key && (submitAreaParams[key] = val);
        }
        const keys = ['country', 'provinceLetterCode', 'cityLetterCode'];
        for (let i = 0; i < keys.length; i++) {
          if (!submitAreaParams[keys[i]]) {
            submitAreaParams[keys[i]] = '';
          }
        }
      } else {
        submitAreaParams.country = params.country;
      }
      /**
       * 验证绑定的国家数据（countryCodeList.value）是否包含当前editOrViewUserInfo.value?.country
       * 1:如果countryCodeList.value=空数组-则不验证
       * 2:如果countryCodeList.value=非空数组-且包含editOrViewUserInfo.value?.country-则不验证
       * 3:如果countryCodeList.value=非空数组-且不包含editOrViewUserInfo.value?.country-则验证
       */
      if (countryCodeList.value.length > 0) {
        if (!countryCodeList.value.includes(editOrViewUserInfo.value?.country)) {
          return createMessage.warning(t('common.guojia_tips'));
        }
      }
      let mobileVal = '';
      if (pageType === PageTypeEnum.ADD) {
        mobileVal = params.mobileNotQuhao ? quhaoVal.value + params.mobileNotQuhao : '';
      } else {
        mobileVal = params.mobile || '';
      }
      // 处理网点数据或B端客户 key 2：b端客户 3网点
      if (params.networkCode) {
        params.networkCodeMap = { '2': params.networkCode.split(',') };
        delete params.networkCode;
      }
      if (params.networkCode1) {
        params.networkCodeMap = { '3': params.networkCode1.split(',') };
        delete params.networkCode1;
      }

      await api({
        ...params,
        networkCode: params.networkCode || params.networkCode1 || '',
        userId,
        ...submitAreaParams,
        mobile: mobileVal,
      });
      success(t('common.operationSuccess'));
      handleCancel();
    } finally {
      submitButtonOptions.value.loading = false;
    }
  };

  // 递归查询到菜单树的所有ID达到默认展开效果
  function getTreeIds(treeData: any, treeIds: string[] = []) {
    treeData.forEach((item: any) => {
      treeIds.push(item.id);
      if (item.children) {
        getTreeIds(item.children, treeIds);
      }
    });
    return treeIds;
  }

  onMounted(async () => {
    resetFields();
    pageType === PageTypeEnum.ADD && (await queryAreaListFn());
    await updateSchema([
      {
        field: 'accountType',
        componentProps: {
          disabled: [PageTypeEnum.VIEW, PageTypeEnum.EDIT].includes(pageType as any),
        },
      },
      {
        field: 'userCode',
        componentProps: {
          disabled: [PageTypeEnum.VIEW, PageTypeEnum.EDIT].includes(pageType as any),
        },
      },
      {
        field: 'mobile',
        ifShow: pageType === PageTypeEnum.ADD ? false : true,
      },
      {
        field: 'mobileNotQuhao',
        ifShow: pageType !== PageTypeEnum.ADD ? false : true,
      },
    ]);
    await getRegionCountryList();
    await updateSchema({
      field: 'regionCode',
      componentProps: {
        options: regionCountryList.value,
      },
    });
    await getCompanyTreeFn();
    // @ts-ignore
    if ([PageTypeEnum.VIEW, PageTypeEnum.EDIT].includes(pageType)) {
      queryUserInfoApi({ userId })
        .then(async (res) => {
          const { menuTree, user } = res || {};
          // 记录用户信息-用途-校验再次编辑用户不让更改国家
          editOrViewUserInfo.value = user;

          // 获取分配的国家数据
          await getCountryList(userId);

          if (PageTypeEnum.VIEW === pageType) {
            menuTreeList.value =
              menuTree?.children?.map((mapItems) => {
                const treeIds = getTreeIds(mapItems?.children || []);
                const selectIds = getTreeIds(mapItems?.children);
                selectMenuIds.value.push(...selectIds);
                let treeData = [] as any;
                treeData = getTreeDisableds(mapItems?.children || []);
                return {
                  title: mapItems.text,
                  id: mapItems.id,
                  treeData,
                  checkedKeys: selectIds,
                  selectedKeys: selectIds,
                  expandedKeys: treeIds,
                };
              }) || [];
          }

          const userInfo = user || {};
          await setProps({
            schemas: [...submitFormSchema],
          });
          if (!!userInfo.country) {
            // 获取国家下拉
            const { buserList, networkList } = await initBuserAndNetworkInfo(userInfo.country);
            // 获取网点信息下拉数据
            await updateSchema([
              {
                field: 'networkCode',
                componentProps: {
                  options: buserList,
                },
              },
              {
                field: 'networkCode1',
                componentProps: {
                  options: networkList,
                },
              },
            ]);
          }
          if (!!userInfo.companyId) {
            const newData = await getDepartTreeFn(userInfo.companyId);
            await updateSchema({
              field: 'deptId',
              componentProps: {
                treeData: newData,
              },
            });
          }
          for (let i in userInfo) {
            if ((userInfo[i] ?? '') === '') {
              userInfo[i] = undefined;
            }
          }
          const { buserList, networkList } = await initBuserAndNetworkInfo(userInfo.country);
          await updateSchema([
            {
              field: 'country',
              ifShow: ['2', '3', '', undefined].includes(userInfo.accountCategory),
              componentProps: {
                options: !userInfo.regionCode
                  ? []
                  : regionCountryList.value.find((findItems) => findItems.value === userInfo.regionCode)?.children || [],
              },
            },
            {
              field: 'country_area_code',
              ifShow: ['1'].includes(userInfo.accountCategory),
              componentProps: {
                treeData: !userInfo.regionCode
                  ? []
                  : regionCountryQuyuList.value.find((findItems) => findItems.value === userInfo.regionCode)?.children || [],
              },
            },
            {
              field: 'networkCode',
              ifShow: ['1', '2', '', undefined].includes(userInfo.accountCategory),
              componentProps: {
                options: buserList,
              },
            },
            {
              field: 'networkCode1',
              ifShow: ['3'].includes(userInfo.accountCategory),
              componentProps: {
                options: networkList,
              },
            },
          ]);

          // 国家/省/市数据回显
          let areaParams: any = {};
          if (['1'].includes(userInfo.accountCategory)) {
            const keys = ['country', 'provinceLetterCode', 'cityLetterCode'];
            const newKeys: any = [];
            for (let i = 0; i < keys.length; i++) {
              if (userInfo[keys[i]]) {
                const thisVal = userInfo[keys[i]];
                newKeys.push(`${thisVal}&&&&${keys[i]}`);
              }
            }
            areaParams.country_area_code = newKeys.join('____');
          } else {
            areaParams.country = userInfo.country;
          }

          const networkCodeMap = userInfo?.networkCodeMap || {};
          await setFieldsValue({
            ...userInfo,
            accountType: userInfo.accountType || '2',
            networkCode1: networkCodeMap[3] || [],
            networkCode: networkCodeMap[2] || [],
            deptId: userInfo.deptCode || null,
            userName: userInfo.username,
            ...areaParams,
          });
          // 刷新操作日志
          if (pageType === PageTypeEnum.VIEW) {
            reload();
          }
          setProps({
            disabled: pageType === PageTypeEnum.VIEW,
          });
        })
        .catch((_err) => {
          // console.log(err);
        });
    } else {
      updateSchema([
        {
          field: 'departType',
          dynamicDisabled: false,
        },
      ]);
    }
  });

  // 返回上一级
  const handleCancel = () => {
    closeTab();
    router.push('/system/user');
  };
</script>
<style lang="less" scoped>
  .user_handle {
    background: @component-background;
    height: calc(100vh - 140px);
    overflow: auto;
  }
</style>
