import { computed } from 'vue';
import { FormState } from './useCronForm';
import { REPEAT_TYPE_ENUM } from '/@/enums/common';
import type { I18nGlobalTranslation } from '/@/hooks/web/useI18n';

/**
 * 计算触发规则描述
 * @param formState 表单状态
 * @param t 翻译函数
 * @param weekDaysOptions 星期几选项
 * @param datePickerLocale 日期选择器本地化配置
 * @returns 触发规则描述
 */
export function useCronRuleDescription(formState: FormState, t: I18nGlobalTranslation, weekDaysOptions: string[], datePickerLocale: any) {
  // 计算触发规则描述
  const ruleDescription = computed(() => {
    if (formState.repeatType === REPEAT_TYPE_ENUM.NO_REPEAT) {
      return t('noRepeat');
    } else {
      const desc = `${t('every')} ${formState.interval} `;
      let result = desc;
      let appendTimeAndTrigger = true; // 控制是否在末尾添加时间和触发字样

      switch (formState.repeatRuleType) {
        case 'hour':
          // 【测试功能】小时规则描述 - 修改为"每 X 小时第 Y 分钟 触发"格式
          const minute = formState.startTime.minute();
          result = `${result}${t('time_hours')} ${t('ordinalNumbers')} ${minute} ${t('time_minutes')} ${t('trigger')}`;
          appendTimeAndTrigger = false; // 小时规则已经包含了触发字样，不需要再添加时间和触发
          break;
        case 'day':
          result = `${result}${t('time_days')}`;
          break;
        case 'week':
          result = `${result}${t('weekOf')}`;
          if (formState.weekDays.length > 0) {
            // 对星期几进行排序处理，使其按周一到周日的顺序排列
            const sortedWeekDays = [...formState.weekDays].sort((a, b) => {
              // 调整排序，使周一(2)排在最前，然后是周二(3)...周日(1)排在最后
              const adjustedA = a === 1 ? 8 : a; // 将周日(1)调整为8，使其排在最后
              const adjustedB = b === 1 ? 8 : b;
              return adjustedA - adjustedB;
            });
            // weekDaysOptions 按照 [周日,周一,...,周六] 顺序排列，索引需要减1
            const weekDayNames = sortedWeekDays.map((day) => weekDaysOptions[day - 1]).join('、');
            result = `${result} ${weekDayNames}`;
          }
          break;
        case 'month':
          result = `${result}${t('monthsOf')}`;
          // 处理多个日期的显示 - 直接按照图中格式展示
          if (formState.monthDays.length > 0) {
            const sortedDays = [...formState.monthDays].sort((a, b) => a - b);
            const daysText = sortedDays.map((day) => ` ${day} ${t('day')}`).join(' ');
            result = `${result}${daysText}, `;
          }
          break;
        case 'year':
          const month = formState.startTime.month() + 1; // dayjs的月份从0开始
          const day = formState.startTime.date();
          result = `${result}${t('yearOf')} ${month} ${datePickerLocale?.lang?.month} ${day} ${t('day')}`;
          break;
      }

      // 非小时规则才添加时间和触发字样
      return appendTimeAndTrigger ? `${result} ${formState.startTime.format('HH:mm')} ${t('trigger')}` : result;
    }
  });

  return {
    ruleDescription,
  };
}
