<template>
  <BasicModal
    :showCancelBtn="true"
    :showOkBtn="true"
    :width="850"
    destroyOnClose
    :ok-button-props="okButtonProps"
    :maximize="false"
    :mask-closable="false"
    :canFullscreen="false"
    @cancel="handleClose"
    @register="register"
    @ok="handleOk"
    :title="title"
  >
    <BasicTable @register="registerTable" :rowSelection="rowSelection" />
  </BasicModal>
</template>
<script lang="ts">
  import { defineComponent, ref, unref, watch } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicColumn, BasicTable } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { getLabelFromDict } from '@ruqi/utils-admin';

  import { RoleTypeOptions, RoleTypeEnum } from '@/enums/roleEnum';
  export default defineComponent({
    components: { BasicModal, BasicTable },
    props: {
      selectedRowKeys: {
        type: Array<number>,
        default: [],
      },
    },
    emits: ['register', 'close', 'confirm'],
    setup(props, { emit }) {
      const { t } = useI18n('common');
      const title = t('selectRole');

      const tableDataSource = ref<Recordable[]>([]);
      const [register, { closeModal }] = useModalInner((data) => {
        tableDataSource.value = data || [];
      });

      const { createMessage } = useMessage();
      // 弹窗的确认按钮状态
      const okButtonProps = ref({
        disabled: false,
      });

      const columns: BasicColumn[] = [
        {
          title: t('role_id_text'),
          dataIndex: 'roleId',
          width: 100,
        },
        {
          title: t('role_name_text'),
          dataIndex: 'roleName',
          width: 100,
        },
        {
          title: t('roleType'),
          dataIndex: 'roleType',
          width: 120,
          customRender: ({ text }) => getLabelFromDict(text, RoleTypeOptions),
        },
      ];

      const { tableContext } = useListPage({
        tableProps: {
          dataSource: tableDataSource,
          maxHeight: 400,
          clickToRowSelect: true,
          columns,
          pagination: false,
          useSearchForm: false,
          showTableSetting: false,
          showActionColumn: false,
          rowKey: 'roleId',
          rowSelection: {
            type: 'checkbox',
            hideSelectAll: true,
          },
        },
      });

      const [registerTable, { getRawDataSource }, { rowSelection, selectedRows, selectedRowKeys }] = tableContext;

      /** 关闭弹窗 */
      const handleClose = () => {
        closeModal();
        emit('close');
      };

      // 判断相同type
      const areRoleKeySame = (list) => {
        const arr = list.filter((item) => item.roleType !== RoleTypeEnum.ALL);
        console.log(arr, list);
        if (arr.length === 0) return true; // 空数组视为相同
        const firstValue = arr[0]?.roleType;
        return arr.every((item) => item?.roleType === firstValue);
      };
      const handleOk = () => {
        if (unref(selectedRowKeys).length === 0) {
          return createMessage.error(t('chooseText'));
        }
        if (!areRoleKeySame(selectedRows.value)) {
          return createMessage.error(t('selectSameRole'));
        }
        emit('confirm', selectedRows.value, selectedRowKeys.value);
        handleClose();
      };

      watch(
        () => props.selectedRowKeys,
        (val: number[]) => {
          selectedRowKeys.value = val ?? [];
        },
        {
          deep: true,
        }
      );

      return {
        register,
        handleClose,
        registerTable,
        rowSelection,
        selectedRows,
        handleOk,
        okButtonProps,
        title,
      };
    },
  });
</script>
