import { Dayjs } from 'dayjs';
import type { I18nGlobalTranslation } from '/@/hooks/web/useI18n';
import { BASE_DAY_JS_FORMAT, FormState } from './useCronForm';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

// 加载dayjs插件
dayjs.extend(utc);
dayjs.extend(timezone);

/**
 * 计算类型，用于区分不同场景下的触发日期计算
 */
export type TriggerCalculationType = 'nextTrigger' | 'minEnd' | 'validation';

/**
 * 计算结果，包含日期和验证信息
 */
export interface TriggerCalculationResult {
  date: Dayjs | undefined | null;
  isValid: boolean;
  message?: string;
  formattedDate?: string;
}

/**
 * 计算选项
 */
export interface TriggerCalculationOptions {
  weekDaysOptions?: string[];
  formatFn?: (date: Dayjs) => string;
}

/**
 * 检查日期是否在选定的星期几内
 * @param {Dayjs} date - dayjs日期对象
 * @param {number[]} weekdays - 星期几数组 (1-7, 1是周日)
 * @returns {boolean} - 是否在星期几内
 */
export const isDateInWeekdays = (date: Dayjs, weekdays: number[]): boolean => {
  if (!weekdays || weekdays.length === 0) return false;
  // dayjs的day()方法返回0-6（0是周日），转换为Cron表达式的1-7
  const cronDay = date.day() === 0 ? 1 : date.day() + 1;
  return weekdays.includes(cronDay);
};

/**
 * 检查日期是否在选定的月份内
 * @param {Dayjs} date - dayjs日期对象
 * @param {number[]} months - 月份数组 (1-12)
 * @returns {boolean} - 是否在月份内
 */
export const isDateInMonths = (date: Dayjs, months: number[]): boolean => {
  if (!months || months.length === 0) return false;
  return months.includes(date.month() + 1); // dayjs月份从0开始
};

/**
 * 寻找下一个满足星期几条件的日期
 * @param {Dayjs} startDate - dayjs日期对象
 * @param {number[]} weekdays - 星期几数组 (1-7, 1是周日)
 * @returns {Dayjs} - 满足条件的日期
 */
export const findNextWeekday = (startDate: Dayjs, weekdays: number[]): Dayjs => {
  if (!weekdays || weekdays.length === 0) return startDate.clone();

  // 将dayjs的day()结果转换为Cron表示法
  const startDayOfWeek = startDate.day() === 0 ? 1 : startDate.day() + 1;
  const sortedWeekdays = [...weekdays].sort((a, b) => a - b);
  const resultDate = startDate.clone();

  // 寻找当周后面的星期几
  let nextDay: number | null = null;
  for (const day of sortedWeekdays) {
    if (day > startDayOfWeek) {
      nextDay = day;
      break;
    }
  }

  // 找到了当周的星期几
  if (nextDay !== null) {
    // 将Cron表示转回dayjs天数差
    // Cron: 1(日),2(一)...7(六)
    // dayjs: 0(日),1(一)...6(六)
    const dayjsNextDay = nextDay === 1 ? 0 : nextDay - 1;
    const dayjsStartDay = startDate.day();
    return resultDate.add(dayjsNextDay - dayjsStartDay, 'day');
  }

  // 找下周的第一个符合的星期几
  const firstDayNextWeek = sortedWeekdays[0];
  const dayjsFirstDay = firstDayNextWeek === 1 ? 0 : firstDayNextWeek - 1;
  const dayjsStartDay = startDate.day();
  return resultDate.add(7 - dayjsStartDay + dayjsFirstDay, 'day');
};

/**
 * 寻找下一个满足月份规则的日期，考虑指定的日期
 */
export const findNextMonthWithTargetDay = (startDate: Dayjs, targetDay: number | number[], interval: number = 1): Dayjs => {
  const resultDate = startDate.clone();
  const currentDay = resultDate.date();
  const currentMonth = resultDate.month();
  const currentYear = resultDate.year();

  // 处理targetDay为数组的情况
  const targetDays = Array.isArray(targetDay) ? [...targetDay] : [targetDay];
  const sortedDays = targetDays.sort((a, b) => a - b);

  // 当前月份天数
  const daysInCurrentMonth = resultDate.daysInMonth();

  // 当前月份内可用的触发日列表（筛选掉超过当月天数的日期）
  const validDaysThisMonth = sortedDays.filter((day) => day <= daysInCurrentMonth);

  // 当天是否是触发日
  const isTodayTriggerDay = validDaysThisMonth.includes(currentDay);

  // 第一种情况：当天就是触发日
  if (isTodayTriggerDay) {
    // 如果间隔为1或未指定，当天就是下一个触发日
    if (interval <= 1) {
      return resultDate;
    }
    // 如果间隔大于1，则需要计算下一个符合间隔的月份中的相同日期
    const nextMonthOffset = interval;
    const nextMonth = (currentMonth + nextMonthOffset) % 12;
    const nextMonthYear = currentYear + Math.floor((currentMonth + nextMonthOffset) / 12);
    const nextMonthDate = resultDate.month(nextMonth).year(nextMonthYear);
    const daysInNextMonth = nextMonthDate.daysInMonth();

    // 确保目标日在下个月的有效范围内
    if (currentDay <= daysInNextMonth) {
      return nextMonthDate.date(currentDay);
    } else {
      // 如果目标日超出了下个月的天数范围，取下个月的最后一天
      return nextMonthDate.date(daysInNextMonth);
    }
  }

  // 第二种情况：当月内有大于当前日期的触发日
  const futureDaysThisMonth = validDaysThisMonth.filter((day) => day > currentDay);
  if (futureDaysThisMonth.length > 0) {
    // 使用当月最早的未来触发日
    return resultDate.date(futureDaysThisMonth[0]);
  }

  // 第三种情况：需要查找下一个月的触发日
  // 计算下个月的年和月，考虑interval
  const nextMonthOffset = interval;
  const nextMonth = (currentMonth + nextMonthOffset) % 12;
  const nextMonthYear = currentYear + Math.floor((currentMonth + nextMonthOffset) / 12);

  const nextMonthDate = resultDate.month(nextMonth).year(nextMonthYear).date(1);
  const daysInNextMonth = nextMonthDate.daysInMonth();

  // 下个月可用的触发日（筛选掉超过下月天数的日期）
  const validDaysNextMonth = sortedDays.filter((day) => day <= daysInNextMonth);

  if (validDaysNextMonth.length > 0) {
    // 使用下个月最早的触发日
    return nextMonthDate.date(validDaysNextMonth[0]);
  }

  // 如果下个月没有有效的触发日，继续查找直到找到有效的触发日
  let searchMonth = nextMonth;
  let searchYear = nextMonthYear;

  while (true) {
    // 移至下一个符合间隔的月份
    searchMonth = (searchMonth + interval) % 12;
    searchYear = searchYear + Math.floor((searchMonth + interval) / 12);

    const searchDate = resultDate.year(searchYear).month(searchMonth).date(1);
    const daysInSearchMonth = searchDate.daysInMonth();

    // 当前搜索月中可用的触发日
    const validDaysSearchMonth = sortedDays.filter((day) => day <= daysInSearchMonth);

    if (validDaysSearchMonth.length > 0) {
      // 找到了有效的触发日
      return searchDate.date(validDaysSearchMonth[0]);
    }

    // 安全检查，防止无限循环（最多查找24个月）
    if ((searchYear - currentYear) * 12 + (searchMonth - currentMonth) > 24) {
      // 实在找不到，返回开始日期
      return startDate.clone();
    }
  }
};

/**
 * 调整日期以适应月份天数
 */
export const adjustDayForMonth = (date: Dayjs, targetDay: number): Dayjs => {
  const daysInMonth = date.daysInMonth();
  if (targetDay > daysInMonth) {
    return date.date(daysInMonth);
  }
  return date.date(targetDay);
};

/**
 * 格式化日期为可读字符串
 */
export const formatDateToString = (date: Dayjs, weekDaysOptions: string[]): string => {
  if (!date) return '';
  const year = date.year();
  const month = date.month() + 1;
  const day = date.date();
  const hour = date.hour();
  const minute = date.minute();
  // 将dayjs的day()结果（0-6）转换为Cron格式的索引（1-7）
  const weekDay = date.day();
  const cronDay = weekDay === 0 ? 1 : weekDay + 1;
  // weekDaysOptions按照Cron顺序排列，所以索引-1即可获取正确的名称
  const weekDayName = weekDaysOptions[cronDay - 1];
  return `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}, ${weekDayName}, ${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
};

/**
 * 规则类型验证函数
 */
export const validateRuleType = {
  /**
   * 验证重复规则配置是否完整
   */
  isConfigValid: (formState: FormState): boolean => {
    // 不重复的情况，配置始终有效
    if (formState.repeatType === 'noRepeat') {
      return true;
    }

    // 重复的情况，检查基本配置
    if (formState.repeatType !== 'repeat' || !formState.repeatRuleType || !formState.interval) {
      return false;
    }

    // 根据不同规则类型进行特定验证
    switch (formState.repeatRuleType) {
      case 'hour':
        // 【测试功能】小时规则只需要有间隔即可
        return true;
      case 'day':
        // 天规则只需要有间隔即可
        return true;
      case 'week':
        // 周规则需要选择星期几
        return formState.weekDays !== undefined && formState.weekDays.length > 0;
      case 'month':
        // 月规则需要选择日期
        return formState.monthDays !== undefined && formState.monthDays.length > 0;
      case 'year':
        // 年规则只需要有间隔即可
        return true;
      default:
        return false;
    }
  },
};

/**
 * 根据不同规则类型计算触发时间的核心函数
 */
export const calculateTriggerDateByRule = (
  formState: FormState,
  calculationType: TriggerCalculationType,
  t: I18nGlobalTranslation,
  options: TriggerCalculationOptions = {}
): TriggerCalculationResult => {
  const { weekDaysOptions } = options;

  // 处理"不重复"情况
  if (formState.repeatType === 'noRepeat') {
    return {
      date: formState.startTime?.clone(),
      isValid: true,
    };
  }

  // 基本参数校验
  if (!validateRuleType.isConfigValid(formState)) {
    return {
      date: null,
      isValid: false,
      message: t('pleaseConfigureRepeatRules'),
    };
  }

  // 如果是minEnd计算类型，直接使用nextTrigger的结果
  if (calculationType === 'minEnd') {
    const nextTriggerResult = calculateTriggerDateByRule(formState, 'nextTrigger', t, options);
    // 确保返回有效日期，防止index.vue中使用备用逻辑
    if (!nextTriggerResult.date) {
      return {
        date: formState.startTime.clone(),
        isValid: nextTriggerResult.isValid,
        message: nextTriggerResult.message,
      };
    }
    return nextTriggerResult;
  }

  let resultDate = formState.startTime.clone();
  let isValid = true;
  let message = '';

  // 根据规则类型计算日期
  switch (formState.repeatRuleType) {
    case 'hour':
      // 【测试功能】小时规则处理
      // 首次触发就是开始时间，无需特殊处理
      break;

    case 'day':
      // 天规则处理
      // 首次触发就是开始时间，无需特殊处理
      break;

    case 'week':
      // 周规则处理
      if (formState.weekDays && formState.weekDays.length > 0) {
        // 将dayjs的day()结果转换为Cron表示法
        const currentDayOfWeek = resultDate.day() === 0 ? 1 : resultDate.day() + 1;
        // 检查当前日期是否满足条件
        if (formState.weekDays.includes(currentDayOfWeek)) {
          // 当前日期符合条件
          // 对于校验，无需额外加一天，使用当前日期即可
        } else {
          // 当前日期不符合条件，查找下一个符合条件的日期
          const nextDate = findNextWeekday(resultDate, formState.weekDays);
          if (calculationType === 'nextTrigger' || calculationType === 'validation') {
            resultDate = nextDate;
          }
        }
      } else {
        isValid = false;
        message = t('pleaseSelectAtLeastOneWeek');
      }
      break;

    case 'month':
      // 月规则处理
      if (formState.monthDays && formState.monthDays.length > 0) {
        const currentDay = resultDate.date();
        const daysInCurrentMonth = resultDate.daysInMonth();

        // 对所选的日期进行排序
        const sortedMonthDays = [...formState.monthDays].sort((a, b) => a - b);

        // 判断当前日期是否为触发日期之一
        const isCurrentDayTrigger = sortedMonthDays.includes(currentDay);

        // 计算下一个触发日期的辅助函数
        const findNextTriggerDate = () => {
          const futureDaysThisMonth = sortedMonthDays.filter((day) => day > currentDay && day <= daysInCurrentMonth);

          if (futureDaysThisMonth.length > 0) {
            // 当月有未来触发日
            const nextDay = futureDaysThisMonth[0];
            let nextDate = resultDate.clone().date(nextDay);

            // 如果间隔大于1，应该跳到下一个间隔月份
            if (formState.interval && formState.interval > 1) {
              const intervalValue = formState.interval;
              nextDate = findNextMonthWithTargetDay(nextDate, nextDay, intervalValue);
            }

            return nextDate;
          } else {
            // 当月没有未来触发日，查找下一个月的触发日
            const intervalValue = formState.interval || 1;
            return findNextMonthWithTargetDay(resultDate.clone(), sortedMonthDays, intervalValue);
          }
        };

        // 针对不同计算类型处理
        if (calculationType === 'nextTrigger' || calculationType === 'validation') {
          if (isCurrentDayTrigger) {
            // 当前日期就是触发日期，直接使用
            // 无需额外操作，resultDate已经是当前日期
          } else {
            // 当前日期不是触发日期，查找下一个触发日期
            resultDate = findNextTriggerDate();
          }
        }
      } else {
        isValid = false;
        message = t('pleaseSelectAtLeastOneDate');
      }
      break;

    case 'year':
      // 年规则处理
      // 首次触发时间就是开始时间，无需特殊处理
      break;
  }

  // 处理结束时间校验
  if (calculationType === 'validation' && formState.endTime) {
    if (formState.endTime.isBefore(resultDate)) {
      isValid = false;
      // 使用formatDateToString格式化日期
      const formattedDate = weekDaysOptions ? formatDateToString(resultDate, weekDaysOptions) : resultDate.format(BASE_DAY_JS_FORMAT);

      message = t('minValidEndTimeStr', { minValidEndTime: formattedDate });
      return {
        date: resultDate,
        isValid,
        message,
      };
    }
  }

  return {
    date: resultDate,
    isValid,
    message,
  };
};

/**
 * 在两个时区之间转换时间
 *
 * 注意：
 * 1. 此函数设计用于在不同时区间转换时间，保持绝对时间点的一致性
 * 2. 自动处理日期变更，包括跨日期线的情况
 * 3. 支持非整点时区（如印度UTC+5:30），通过小数小时计算
 * 4. 确保用户在不同时区看到的是相同时间点对应的本地时间
 *
 * @param date - 要转换的日期对象
 * @param fromTimezone - 源时区
 * @param toTimezone - 目标时区
 * @returns - 转换后的日期对象
 */
export const convertDateBetweenTimezones = (date: Dayjs, fromTimezone: string, toTimezone: string): Dayjs => {
  // 简单防御：时区相同直接返回
  if (fromTimezone === toTimezone) return date.clone();

  // 参数校验：确保输入有效
  if (!date || !date.isValid()) {
    console.error('时区转换错误: 无效的日期对象');
    return dayjs(); // 返回当前时间作为防御
  }

  // 时区参数验证
  if (!fromTimezone || !toTimezone) {
    console.error('时区转换错误: 时区参数缺失', { fromTimezone, toTimezone });
    return date.clone();
  }

  try {
    // 方法：手动创建一个具有相同年月日时分秒的日期，但明确指定为源时区
    const year = date.year();
    const month = date.month();
    const day = date.date();
    const hour = date.hour();
    const minute = date.minute();
    const second = date.second();

    // 明确将日期解释为源时区的日期
    const sourceDate = dayjs.tz(`${year}-${month + 1}-${day} ${hour}:${minute}:${second}`, fromTimezone);

    // 转换到目标时区
    const targetDate = sourceDate.tz(toTimezone);

    // 获取时区偏移值（使用dayjs提供的方法）
    const fromOffsetMinutes = dayjs().tz(fromTimezone).utcOffset();
    const toOffsetMinutes = dayjs().tz(toTimezone).utcOffset();
    const offsetDiffHours = (toOffsetMinutes - fromOffsetMinutes) / 60;

    // 检测是否跨日期线 (时差绝对值超过12小时)
    const isDateLineCrossing = Math.abs(offsetDiffHours) > 12;

    // 记录调试信息
    console.debug(`---- 时区转换详情（重写版）----`);
    console.debug(`- 源时区: ${fromTimezone} (UTC${fromOffsetMinutes >= 0 ? '+' : ''}${(fromOffsetMinutes / 60).toFixed(2)})`);
    console.debug(`- 目标时区: ${toTimezone} (UTC${toOffsetMinutes >= 0 ? '+' : ''}${(toOffsetMinutes / 60).toFixed(2)})`);
    console.debug(`- 时区差: ${offsetDiffHours.toFixed(2)} 小时${isDateLineCrossing ? ' 【跨日期线】' : ''}`);
    console.debug(`- 源日期组件: ${year}-${month + 1}-${day} ${hour}:${minute}:${second}`);
    console.debug(`- 源时区日期: ${sourceDate.format('YYYY-MM-DD HH:mm:ss')}`);
    console.debug(`- 源时区UTC: ${sourceDate.toISOString()}`);
    console.debug(`- 目标时区日期: ${targetDate.format('YYYY-MM-DD HH:mm:ss')}`);
    console.debug(`- 目标时区UTC: ${targetDate.toISOString()}`);
    console.debug(`----------------------`);

    return targetDate;
  } catch (error) {
    console.error('时区转换错误:', error);
    // 出错时返回原始日期的克隆
    return date.clone();
  }
};

/**
 * 获取用户系统时区
 */
export const getUserSystemTimezone = (): string => {
  return Intl.DateTimeFormat().resolvedOptions().timeZone;
};

/**
 * 服务器时区常量
 */
export const SERVER_TIMEZONE = 'Asia/Shanghai';

/**
 * 处理月份日期在跨天时区情况下的调整
 * @param days 需要调整的日期数组
 * @param isAddOneDay 是否需要加一天
 * @param referenceDate 参考日期，用于获取月份信息，默认为当前月
 * @returns 调整后的日期数组
 */
export const adjustMonthDaysForCrossDayTimezone = (days: number[], isAddOneDay: boolean, referenceDate: Dayjs = dayjs()): number[] => {
  if (!days || days.length === 0) return [];

  const currentMonth = referenceDate.month();
  // 获取当前月和下一个月的天数
  const daysInCurrentMonth = referenceDate.daysInMonth();
  // const nextMonth = currentMonth === 11 ? 0 : currentMonth + 1;
  // const nextMonthDays = referenceDate.month(nextMonth).daysInMonth();
  // 获取前一个月的天数
  const prevMonth = currentMonth === 0 ? 11 : currentMonth - 1;
  const prevMonthDays = referenceDate.month(prevMonth).daysInMonth();

  return days.map((day) => {
    if (isAddOneDay) {
      // 需要加1天，处理月末情况
      if (day >= daysInCurrentMonth) {
        return 1; // 月末日期加1天变成下月1日
      }
      return day + 1;
    } else {
      // 需要减1天，处理月初情况
      if (day <= 1) {
        return prevMonthDays; // 月初1日减1天变成上月最后一天
      }
      return day - 1;
    }
  });
};

/**
 * 处理年规则在跨天时区情况下的日期调整
 *
 * @param serverDate 服务器时区日期对象
 * @param isAddOneDay 是否需要加一天（由handleCrossDayTimezoneRules函数根据跨天情况判断）
 * @param userDate 用户时区日期对象（可选）
 * @returns 调整后的月份日期信息
 */
export interface YearRuleAdjustmentResult {
  month: number; // 调整后的月份 (1-12)
  day: number; // 调整后的日期
  yearOffset: number; // 年份变化量 (-1, 0, 1)
}

export const adjustYearRuleForCrossDayTimezone = (serverDate: Dayjs, isAddOneDay: boolean, userDate?: Dayjs): YearRuleAdjustmentResult => {
  // 特殊情况检查：用户选择了12月31日，且需要增加一天
  if (userDate && isAddOneDay && userDate.month() === 11 && userDate.date() === 31) {
    console.debug(`年规则特殊情况：用户选择了12月31日，且需要增加一天`);
    console.debug(`用户时区: ${userDate.format('YYYY-MM-DD HH:mm:ss')}`);
    console.debug(`服务器时区: ${serverDate.format('YYYY-MM-DD HH:mm:ss')}`);

    // 12月31日加一天一定是1月1日，年份加1
    return {
      month: 1,
      day: 1,
      yearOffset: 1,
    };
  }

  // 服务器日期信息
  const currentMonth = serverDate.month() + 1; // 转为1-12月表示
  const currentDay = serverDate.date();
  const daysInMonth = serverDate.daysInMonth();

  // 处理日期加减
  if (isAddOneDay) {
    // 处理日期加1
    if (currentDay >= daysInMonth) {
      // 月底情况：进入下个月的第1天
      const nextMonth = currentMonth === 12 ? 1 : currentMonth + 1;
      return {
        month: nextMonth,
        day: 1,
        yearOffset: nextMonth === 1 ? 1 : 0, // 如果从12月到1月则年份加1
      };
    } else {
      // 普通情况：日期加1，月份不变
      return {
        month: currentMonth,
        day: currentDay + 1,
        yearOffset: 0, // 年份不变
      };
    }
  } else {
    // 处理日期减1的情况
    // 注意：在当前服务器UTC+8的配置下，这种情况极少发生
    console.debug(`罕见情况: 年规则需要减一天调整 (服务器UTC+8)`);

    if (currentDay <= 1) {
      // 月初情况：变成上个月的最后一天
      const prevMonth = currentMonth === 1 ? 12 : currentMonth - 1;
      // 获取上月天数
      const prevMonthDays = serverDate.clone().subtract(1, 'month').daysInMonth();

      return {
        month: prevMonth,
        day: prevMonthDays,
        yearOffset: prevMonth === 12 ? -1 : 0, // 如果从1月到12月则年份减1
      };
    } else {
      // 普通情况：日期减1，月份不变
      return {
        month: currentMonth,
        day: currentDay - 1,
        yearOffset: 0, // 年份不变
      };
    }
  }
};

/**
 * 处理跨天时区的通用函数
 * 用于调整基于日期的重复规则（月规则、周规则等）在跨天时区情况下的正确表示
 *
 * @param userDate 用户时区日期
 * @param serverDate 服务器时区日期
 * @param ruleName 规则名称，用于日志标识
 * @param adjustFn 根据调整方向进行实际调整的回调函数
 * @param isReverse 是否反向调整（解析时为true，生成表达式时为false）
 * @returns 是否进行了调整
 */
export const handleCrossDayTimezoneRules = (
  userDate: Dayjs,
  serverDate: Dayjs,
  ruleName: string,
  adjustFn: (isAddOneDay: boolean) => void,
  isReverse = false
): boolean => {
  // 检测是否跨天
  const userDateStr = userDate.format('YYYY-MM-DD');
  const serverDateStr = serverDate.format('YYYY-MM-DD');
  const hasCrossedDay = userDateStr !== serverDateStr;

  if (!hasCrossedDay) {
    return false; // 没有跨天，无需调整
  }

  // 记录调试信息
  console.debug(`${ruleName}跨天检测:`);
  console.debug(`用户时区日期: ${userDateStr} (${userDate.format('HH:mm:ss')})`);
  console.debug(`服务器时区日期: ${serverDateStr} (${serverDate.format('HH:mm:ss')})`);

  // 确定跨天方向
  const isServerDateEarlier = serverDateStr < userDateStr; // 服务器日期比用户日期早
  const isServerDateLater = serverDateStr > userDateStr; // 服务器日期比用户日期晚

  console.debug(`服务器时间比用户时间早: ${isServerDateEarlier}`);
  console.debug(`服务器时间比用户时间晚: ${isServerDateLater}`);

  // 对于当前配置：服务器时区为UTC+8 (SERVER_TIMEZONE = 'Asia/Shanghai')
  // 通常情况下，只会出现服务器时区比用户时区晚的情况(isServerDateLater=true)
  // 例如：用户在UTC+6:30(缅甸)设置23:00，服务器UTC+8时间为00:30(第二天)

  // 确定调整方向
  // 生成表达式时: 服务器早于用户 -> 减1, 服务器晚于用户 -> 加1
  // 解析表达式时: 服务器早于用户 -> 加1, 服务器晚于用户 -> 减1 (反向调整)
  const shouldDecrease = isReverse ? isServerDateLater : isServerDateEarlier;
  const shouldIncrease = isReverse ? isServerDateEarlier : isServerDateLater;

  // 执行调整回调
  // 注意：在当前配置(服务器UTC+8)下，shouldDecrease总是false，因为isServerDateEarlier几乎不会发生
  // 保留此逻辑是为了未来服务器时区配置可能发生变化的情况
  if (shouldDecrease) {
    console.debug(`调整方向: 减一天 (极少发生的情况，服务器UTC+8)`);
    adjustFn(false); // 减一天
    return true;
  }

  if (shouldIncrease) {
    console.debug(`调整方向: 加一天 (常见情况，如用户UTC+6:30的23:00变为服务器UTC+8的00:30)`);
    adjustFn(true); // 加一天
    return true;
  }

  return false;
};
