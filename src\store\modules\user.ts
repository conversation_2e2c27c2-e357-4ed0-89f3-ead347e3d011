import type { UserInfo } from '/#/store';
import type { ErrorMessageMode } from '/#/axios';
import { defineStore } from 'pinia';
import { store } from '/@/store';
import { isMobile } from '/@/utils';
import { PageEnum } from '/@/enums/pageEnum';
import { TOKEN_KEY, USER_INFO_KEY, ACCESS_TOKEN_KEY } from '/@/enums/cacheEnum';
import { getAuthCache, setAuthCache } from '/@/utils/auth';
import { GetUserInfoModel, LoginParams, ThirdLoginParams } from '/@/api/sys/model/userModel';
import { doLogout, getUserInfo, loginApi, thirdLogin } from '/@/api/sys/user';
import { useI18n } from '/@/hooks/web/useI18n';
import { useMessage } from '/@/hooks/web/useMessage';
import { router } from '/@/router';
import { useGlobSetting } from '/@/hooks/setting';
import { JDragConfigEnum } from '/@/enums/jeecgEnum';
import { useSso } from '/@/hooks/web/useSso';
import { message } from 'ant-design-vue';
import { AnyCaaRecord } from 'dns';
interface UserState {
  userInfo: Nullable<UserInfo>;
  token?: string;
  accessToken?: string;
  sessionTimeout?: boolean;
  lastUpdateTime: number;
}

export const useUserStore = defineStore({
  id: 'app-user',
  state: (): UserState => ({
    // 用户信息
    userInfo: getAuthCache<UserInfo>(USER_INFO_KEY) != null ? getAuthCache<UserInfo>(USER_INFO_KEY) : null,
    // token
    token: getAuthCache<string>(TOKEN_KEY),
    // accessToken  DMS登录token
    accessToken: getAuthCache<string>(ACCESS_TOKEN_KEY),
    // session过期时间
    sessionTimeout: false,
    // Last fetch time
    lastUpdateTime: 0,
  }),
  getters: {
    getUserInfo(): UserInfo {
      if (this.userInfo == null) {
        this.userInfo = getAuthCache<UserInfo>(USER_INFO_KEY) != null ? getAuthCache<UserInfo>(USER_INFO_KEY) : null;
      }
      return this.userInfo || getAuthCache<UserInfo>(USER_INFO_KEY) || {};
    },
    getToken(): string {
      return this.token || getAuthCache<string>(TOKEN_KEY);
    },
    getAccessToken(): string {
      return this.getAccessToken || getAuthCache<string>(ACCESS_TOKEN_KEY);
    },
    getSessionTimeout(): boolean {
      return !!this.sessionTimeout;
    },
    getLastUpdateTime(): number {
      return this.lastUpdateTime;
    },
  },
  actions: {
    setToken(info: string | undefined) {
      this.token = info ? info : ''; // for null or undefined value
      setAuthCache(TOKEN_KEY, info);
    },
    setAccessToken(info: string | undefined) {
      this.accessToken = info ? info : ''; // for null or undefined value
      setAuthCache(ACCESS_TOKEN_KEY, info);
    },
    setUserInfo(info: UserInfo | null) {
      this.userInfo = info;
      this.lastUpdateTime = new Date().getTime();
      setAuthCache(USER_INFO_KEY, info);
    },
    setSessionTimeout(flag: boolean) {
      this.sessionTimeout = flag;
    },
    resetState() {
      this.userInfo = null;
      this.token = '';
      this.sessionTimeout = false;
    },
    /**
     * 登录事件
     */
    async login(
      params: LoginParams & {
        goHome?: boolean;
        mode?: ErrorMessageMode;
        isH5?: boolean;
      }
    ): Promise<GetUserInfoModel | null> {
      try {
        const { goHome = true, isH5 = false, mode, ...loginParams } = params;
        const data: any = await loginApi(loginParams, mode);
        this.setToken(data);
        return this.afterLoginAction(goHome, data, isH5);
      } catch (error) {
        return Promise.reject(error);
      }
    },
    /**
     * 登录完成处理
     * @param goHome
     */
    async afterLoginAction(goHome?: boolean, data?: any, isH5 = false): Promise<any | null> {
      if (!this.getToken) return null;
      // 获取用户信息
      const userInfo = await this.getUserInfoAction();
      // 根据用户信息判定是否第一次登录，如果是第一次登录需要跳转登录页面
      // if (goHome && userInfo?.needChangePwd === 1) {
      //   userInfo.homePath = '/workbenches';
      // }
      const sessionTimeout = this.sessionTimeout;
      if (sessionTimeout) {
        this.setSessionTimeout(false);
      } else {
        //update-begin-author:liusq date:2022-5-5 for:登录成功后缓存拖拽模块的接口前缀
        localStorage.setItem(JDragConfigEnum.DRAG_BASE_URL, useGlobSetting().domainUrl);
        //update-end-author:liusq date:2022-5-5 for: 登录成功后缓存拖拽模块的接口前缀

        // update-begin-author:sunjianlei date:20230306 for: 修复登录成功后，没有正确重定向的问题
        let redirect = router.currentRoute.value?.query?.redirect as string;
        // 判断是否有 redirect 重定向地址
        //update-begin---author:wangshuai ---date:20230424  for：【QQYUN-5195】登录之后直接刷新页面导致没有进入创建组织页面------------
        if (redirect && goHome) {
          //update-end---author:wangshuai ---date:20230424  for：【QQYUN-5195】登录之后直接刷新页面导致没有进入创建组织页面------------
          // update-begin--author:liaozhiyang---date:20240104---for：【QQYUN-7804】部署生产环境，登录跳转404问题
          let publicPath = import.meta.env.VITE_PUBLIC_PATH;
          if (publicPath && publicPath != '/') {
            // update-begin--author:liaozhiyang---date:20240509---for：【issues/1147】登录跳转时去掉发布路径的最后一个/以解决404问题
            if (publicPath.endsWith('/')) {
              publicPath = publicPath.slice(0, -1);
            }
            redirect = publicPath + redirect;
          }
          // update-end--author:liaozhiyang---date:20240509---for：【issues/1147】登录跳转时去掉发布路径的最后一个/以解决404问题
          // 当前页面打开
          window.open(redirect, '_self');
          return data;
        }
        // 是否是H5模式
        if (isH5) {
          // 是否是店端管理员
          const toUrl = userInfo?.roleRespList?.find((findItems) => findItems.roleSign === 'dealer_admin')
            ? PageEnum.H5_HOME
            : PageEnum.H5_TO_DO_CENTER;
          router.replace(toUrl);
          return data;
        }
        // update-end-author:sunjianlei date:20230306 for: 修复登录成功后，没有正确重定向的问题
        goHome && (await router.replace((userInfo && userInfo.homePath) || PageEnum.BASE_HOME));
        //update-end---author:wangshuai---date:2024-04-03---for:【issues/1102】设置单点登录后页面，进入首页提示404，也没有绘制侧边栏 #1102---
      }
      return data;
    },
    /**
     * 获取用户信息
     */
    async getUserInfoAction(): Promise<UserInfo | null> {
      if (!this.getToken) {
        return null;
      }
      // @ts-ignore
      const userRes: any = await getUserInfo();
      this.setUserInfo({ ...userRes });
      if (!userRes) {
        message.error('获取登录用户信息失败');
      }
      return userRes;
    },
    /**
     * 退出登录
     */
    async logout(goLogin = false) {
      if (this.getToken) {
        await doLogout();
      }

      this.setToken('');
      setAuthCache(TOKEN_KEY, null);
      this.setSessionTimeout(false);
      this.setUserInfo(null);
      //update-begin-author:liusq date:2022-5-5 for:退出登录后清除拖拽模块的接口前缀
      localStorage.removeItem(JDragConfigEnum.DRAG_BASE_URL);
      //update-end-author:liusq date:2022-5-5 for: 退出登录后清除拖拽模块的接口前缀

      //如果开启单点登录,则跳转到单点统一登录中心
      const openSso = useGlobSetting().openSso;
      if (openSso == 'true') {
        await useSso().ssoLoginOut();
      }
      // update-begin-author:sunjianlei date:20230306 for: 修复登录成功后，没有正确重定向的问题
      if (goLogin) {
        const obj: any = {
          path: !isMobile() ? PageEnum.BASE_LOGIN : PageEnum.H5_LOGIN,
        };
        // 登录成功后跳转到工作台，移动端H5不携带重定向参数
        if (!isMobile()) {
          obj.query = {
            redirect: '/workbenches',
          };
        }
        await router.push(obj);
      } else {
        await router.push({
          path: !isMobile() ? PageEnum.BASE_LOGIN : PageEnum.H5_LOGIN,
        });
      }
      // update-end-author:sunjianlei date:20230306 for: 修复登录成功后，没有正确重定向的问题
    },
    /**
     * 登录事件
     */
    async ThirdLogin(
      params: ThirdLoginParams & {
        goHome?: boolean;
        mode?: ErrorMessageMode;
      }
    ): Promise<any | null> {
      try {
        const { goHome = true, mode, ...ThirdLoginParams } = params;
        const data = await thirdLogin(ThirdLoginParams, mode);
        //update-begin---author:wangshuai---date:2024-07-01---for:【issues/6652】开启租户数据隔离，接入钉钉后登录默认租户为0了---
        const { token } = data;
        //update-end---author:wangshuai---date:2024-07-01---for:【issues/6652】开启租户数据隔离，接入钉钉后登录默认租户为0了---
        // save token
        this.setToken(token);
        return this.afterLoginAction(goHome, data);
      } catch (error) {
        return Promise.reject(error);
      }
    },
    /**
     * 退出询问
     */
    confirmLoginOut() {
      const { t } = useI18n();
      const { createConfirm } = useMessage();
      createConfirm({
        iconType: 'warning',
        title: t('common.app_logoutTip'),
        content: t('common.app_logoutMessage'),
        onOk: async () => {
          await this.logout(true);
        },
      });
    },
  },
});

// Need to be used outside the setup
export function useUserStoreWithOut() {
  return useUserStore(store);
}
