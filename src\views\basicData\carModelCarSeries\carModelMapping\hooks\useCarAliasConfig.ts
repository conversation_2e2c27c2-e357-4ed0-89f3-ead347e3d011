import { useModal } from '/@/components/Modal';
import { useI18n } from '/@/hooks/web/useI18n';
import { computed, nextTick, ref, unref } from 'vue';
import { carAliasOperate, CarAliasOperateParamsType, CarAliasResultType, getCarAliasDetail } from '/@/api/basicData/carModelCarSeries';
import { awaitTo } from '@ruqi/utils-admin';
import { CarAliasEnum, CarAliasType } from '/@/enums/basicData';
import { useNationOptions } from '/@/hooks/common';
import { useMessage } from '/@/hooks/web/useMessage';
import { BasicTableProps } from '/@/components/Table';
import { sleep } from '/@/utils';
import { EditTableModalInstance } from '../components/types';
import { uniqueId } from 'lodash-es';

/**
 * 别名配置
 * @param config 配置信息
 * @returns
 */
export const useCarAliasConfig = () => {
  const { t } = useI18n('common');

  const [registerEditTableModal, { openModal }] = useModal();

  const { nationOptions } = useNationOptions({
    immediate: true,
    enableSearchCity: false,
  });

  // 编辑表格的配置
  const editTableProps: BasicTableProps = {
    expandColumnWidth: 0,
    columns: [
      {
        title: t('serialNumber'),
        width: 100,
        resizable: true,
        customRender: ({ record }) => record?.index + 1,
      },
      {
        title: t('country'),
        dataIndex: 'countryCodes',
        width: 400,
        resizable: true,
        editRow: true,
        editComponent: 'ApiSelect',
        editComponentProps: {
          mode: 'multiple',
          api: loadSelectOptions,
          loadInVisible: true,
          immediate: false,
          maxTagCount: 2,
          allowClear: true,
          showSearch: true,
          getPopupContainer: () => document.body, // 菜单渲染父节点。默认渲染到 body 上，如果你遇到菜单滚动定位问题，试试修改为滚动的区域，并相对其定位。
        },
      },
      {
        title: t('alias'),
        dataIndex: 'alias',
        width: 150,
        resizable: true,
        editRow: true,
        editComponent: 'Input',
      },
    ],
    dataSource: [],
    rowKey: 'uniqueId',
    useSearchForm: false,
    showTableSetting: false,
    showActionColumn: true,
    formConfig: {
      autoSubmitOnEnter: false,
    },
    minHeight: 450,
    scroll: {
      x: 'max-content',
      y: 350,
    },
    actionColumn: {
      title: t('operation'),
      width: 100,
    },
  };

  const { createMessage } = useMessage();

  const modalTitle = ref('');

  const currentEditRow = ref<Recordable>();

  const selectedCountryCodes = ref<string[]>([]); // 存储所有选中的国家代码

  // 存储一些信息
  const currentAliasInfo = ref({
    type: '', // 别名类型
    code: '', // 编码（品牌、车系、车型的code）
  });

  const editTableModalRef = ref<EditTableModalInstance>();

  const submitParams = ref<CarAliasOperateParamsType | null>(null);

  const currentEditRowCountryCodes = computed(() => {
    return currentEditRow.value?.editValueRefs?.countryCodes ?? [];
  });

  const allSelectedCountryCodes = computed(() => {
    return (
      unref(editTableModalRef)
        ?.editTableMethods?.getDataSource()
        ?.map((item) => item?.editValueRefs?.countryCodes ?? [])
        .flat() ?? []
    );
  });

  /**
   * 加载国家代码选项
   * @returns 国家代码选项
   */
  async function loadSelectOptions() {
    // 这个函数会频繁调用, 所以需要优化
    console.log('loadSelectOptions');
    await sleep(200);
    // 根据当前的编辑行, 去过滤国家代码和禁用状态
    const currentCountryCodes = currentEditRowCountryCodes.value;
    const restCountryCodes = allSelectedCountryCodes.value.filter((item) => !currentCountryCodes.includes(item));
    const options = nationOptions.value.map((item) => {
      if (currentCountryCodes.includes(item.value as string)) {
        return {
          ...item,
          disabled: false,
        };
      }
      return {
        ...item,
        disabled: restCountryCodes.includes(item.value as string),
      };
    });
    return options;
  }

  // 获取别名配置
  const fetchAliasByType = async (aliasType: CarAliasType, code: string) => {
    modalTitle.value = aliasType === CarAliasEnum.BRAND ? t('setBrandAlias') : t('setCarSeriesAlias');
    openModal();
    nextTick(async () => {
      editTableModalRef.value?.editTableMethods?.setLoading(true);
      const [err, content] = await awaitTo(
        getCarAliasDetail({
          type: aliasType,
          code,
        })
      );
      await sleep(200);
      if (!err) {
        const editTableMethods = unref(editTableModalRef)?.editTableMethods;
        selectedCountryCodes.value = []; // 清空选中的国家代码
        editTableMethods?.setTableData(
          (content as unknown as CarAliasResultType[])?.map((item, index) => {
            selectedCountryCodes.value.push(...(item.countryCodes ?? []));
            return {
              ...item,
              uniqueId: uniqueId(),
              index,
            };
          })
        );
        currentAliasInfo.value = {
          code,
          type: aliasType.toString(),
        };
      }
      editTableModalRef.value?.editTableMethods?.setLoading(false);
    });
  };

  /**
   * 将数据源转换为参数
   * @param dataSource 数据源
   * @returns 参数
   */
  const parseDataSourceToParams = (dataSource: Recordable[]) => {
    const params: CarAliasOperateParamsType = {
      code: currentAliasInfo.value.code,
      type: Number(currentAliasInfo.value.type),
      aliasList: [],
    };
    if (dataSource.length === 0) {
      params.aliasList = [];
    } else {
      for (const item of dataSource) {
        const alias = item.editValueRefs?.alias || '';
        if (alias === '' || item.editValueRefs?.countryCodes.length === 0) {
          createMessage.error(t('verifyAllTableNotEmpty'));
          return null;
        }
        if (item.editValueRefs?.alias.length > 50) {
          createMessage.error(t('aliasLengthLimit'));
          return null;
        }
        if (params.aliasList.some((item) => item.alias === alias.trim())) {
          createMessage.error(t('aliasDuplicate'));
          return null;
        }
        params.aliasList.push({
          countryCodes: item.editValueRefs?.countryCodes,
          alias: alias.trim(),
        });
      }
    }
    // 需要最终校验一遍国家不能重复
    const countryCodes = params.aliasList.map((item) => item.countryCodes).flat();
    const uniqueCountryCodes = [...new Set(countryCodes)];
    if (countryCodes.length !== uniqueCountryCodes.length) {
      createMessage.error(t('countryDuplicate'));
      return null;
    }
    return params;
  };

  /**
   * 验证数据源
   * @param dataSource 数据源
   * @returns 是否验证通过
   */
  const validateDataSource = (dataSource: Recordable[]) => {
    submitParams.value = parseDataSourceToParams(dataSource);
    if (!submitParams.value) {
      return false;
    }
    return true;
  };

  const handleEditOk = async (callback: () => void) => {
    const params = submitParams.value;
    const [err] = await awaitTo(carAliasOperate(params));
    if (!err) {
      createMessage.success(t('operationSuccess'));
    }
    callback?.();
  };

  const handleClickRow = (record: Recordable) => {
    if (currentEditRow.value?.uniqueId === record?.uniqueId) {
      return;
    }
    currentEditRow.value = record;
  };

  const afterDeleteTableDataRecord = (record: Recordable) => {
    selectedCountryCodes.value = selectedCountryCodes.value.filter((item) => !record?.editValueRefs?.countryCodes?.includes(item));
  };

  return {
    registerEditTableModal,
    fetchAliasByType,
    editTableProps,
    validateDataSource,
    nationOptions,
    modalTitle,
    handleEditOk,
    loadSelectOptions,
    handleClickRow,
    afterDeleteTableDataRecord,
    editTableModalRef,
  };
};
