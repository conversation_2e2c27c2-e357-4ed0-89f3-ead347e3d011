<template>
  <div class="cancel-container">
    <div class="cancel-title">{{ t('common.cancel_dingyue_text') }}</div>
    <div class="cancel-desc"> {{ t('common.dingyue_tips_text') }} </div>
    <div class="cancel-checkbox-group">
      <van-checkbox v-model="allChecked" @change="onAllChange">{{ t('common.all_cancel') }}</van-checkbox>
      <van-checkbox v-model="checkedList.websiteSubscriptionPhone" @change="onItemChange">{{ t('common.phone_text') }}</van-checkbox>
      <van-checkbox v-model="checkedList.subscriptionSms" @change="onItemChange">{{ t('common.sms') }}</van-checkbox>
      <van-checkbox v-model="checkedList.websiteSubscriptionEmail" @change="onItemChange">{{ t('common.youjian_text') }}</van-checkbox>
    </div>
    <div class="cancel-btn-group">
      <van-button class="btn-cancel" block plain type="default" @click="onCancel">{{ t('common.cancelText') }}</van-button>
      <van-button
        class="btn-confirm"
        block
        type="primary"
        :disabled="!checkedList.websiteSubscriptionPhone && !checkedList.subscriptionSms && !checkedList.websiteSubscriptionEmail"
        @click="onConfirm"
        >{{ t('common.confirm_text') }}</van-button
      >
    </div>
  </div>
</template>

<script setup name="cancelarAssinatura" lang="ts">
  // 10061002=已经订阅消息  10061001=取消了订阅消息
  import { ref, watch, onMounted } from 'vue';
  import 'vant/lib/index.css';
  import { useRouter, useRoute } from 'vue-router';
  import { LOCALE } from '@/settings/localeSetting';
  import { useLocale } from '/@/locales/useLocale';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { Checkbox as VanCheckbox, Button as VanButton, showToast } from 'vant';
  import { getCancelDingyueApi, userUpdateSubscriptionApi } from './index.api';

  const router = useRouter();
  const route = useRoute();
  const encryptOneId = ref(route?.query?.oneId as string);
  let languageStr: any = route?.query?.language || 'en-US';
  const { changeLocale } = useLocale();
  const gjStr = languageStr.split('-')[1];
  if (LOCALE[gjStr]) {
    changeLocale(languageStr);
  } else {
    languageStr = 'en-US';
    changeLocale('en-US');
  }
  const { t } = useI18n('', languageStr);

  const checkedList = ref({
    websiteSubscriptionPhone: true,
    subscriptionSms: true,
    websiteSubscriptionEmail: true,
  });
  const allChecked = ref(false);

  // 全部取消切换
  function onAllChange(val) {
    checkedList.value.websiteSubscriptionPhone = val;
    checkedList.value.subscriptionSms = val;
    checkedList.value.websiteSubscriptionEmail = val;
  }
  // 单项切换时判断是否全选
  function onItemChange() {
    allChecked.value = checkedList.value.websiteSubscriptionPhone && checkedList.value.subscriptionSms && checkedList.value.websiteSubscriptionEmail;
  }

  watch(
    checkedList,
    () => {
      allChecked.value =
        checkedList.value.websiteSubscriptionPhone && checkedList.value.subscriptionSms && checkedList.value.websiteSubscriptionEmail;
    },
    { deep: true }
  );

  function onCancel() {
    router.back();
  }
  async function onConfirm() {
    if (!encryptOneId.value) {
      showToast(t('common.not_user_info_text'));
      return;
    }
    if (!checkedList.value.websiteSubscriptionPhone && !checkedList.value.subscriptionSms && !checkedList.value.websiteSubscriptionEmail) {
      showToast(t('common.atLeastOneCondition'));
      return;
    }
    try {
      let submitParams = {};
      for (let i in checkedList.value) {
        submitParams[i] = checkedList.value[i] ? 10061002 : 10061001;
      }
      await userUpdateSubscriptionApi({ ...submitParams, encryptOneId: encryptOneId.value });
      showToast(t('common.cancel_dingyue_success_text'));
    } catch (err) {
      // console.log(err);
    }
  }

  onMounted(async () => {
    if (!encryptOneId.value) {
      return;
    }
    try {
      // 获取当前oneId订阅的信息
      const res = await getCancelDingyueApi({
        encryptOneId: encryptOneId.value,
      });
      for (let i in res) {
        if (res[i] == 10061002) {
          checkedList.value[i] = true;
        } else if (res[i] == 10061001) {
          checkedList.value[i] = false;
        } else {
          checkedList.value[i] = true;
        }
      }
      allChecked.value =
        checkedList.value.websiteSubscriptionPhone && checkedList.value.subscriptionSms && checkedList.value.websiteSubscriptionEmail;
    } catch (err) {}
  });
</script>

<style scoped>
  .cancel-container {
    max-width: 400px;
    padding: 24px 16px 0 16px;
    background: #fff;
    border-radius: 8px;
    box-sizing: border-box;
    height: 100%;
  }
  .cancel-title {
    font-size: 22px;
    font-weight: bold;
    margin-bottom: 16px;
  }
  .cancel-desc {
    font-size: 14px;
    color: #333;
    margin-bottom: 24px;
  }
  .cancel-checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 32px;
  }
  .cancel-btn-group {
    display: flex;
    gap: 12px;
  }
  .btn-cancel {
    flex: 1;
    border-radius: 4px;
  }
  .btn-confirm {
    flex: 1;
    border-radius: 4px;
  }
</style>
