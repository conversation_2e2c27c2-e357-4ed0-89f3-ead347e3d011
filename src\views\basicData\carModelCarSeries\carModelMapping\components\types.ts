import { ActionItem, BasicTableProps, TableActionType } from '/@/components/Table';

export interface EditTableModalInstance {
  editTableMethods: TableActionType;
}

export interface EditTableModalProps {
  // 表格配置
  tableProps: BasicTableProps & {
    renderTableTitle?: (editTableMethods: TableActionType) => ActionItem[];
    renderTableActions?: (record: Recordable) => ActionItem[];
  };
  // 模态框标题
  modalTitle: string;
  // 校验数据源
  validateDataSource: () => void;
  // 是否显示确定按钮
  showOkBtn: boolean;
  // 模态框宽度
  modalWidth: number;
  // 获取插入表格数据记录
  getInsertTableDataRecord: () => Recordable;
  // 删除表格数据记录后回调
  afterDeleteTableDataRecord: (record: Recordable) => void;
}
