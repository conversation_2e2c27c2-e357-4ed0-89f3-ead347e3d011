<template>
  <page-wrapper>
    <BasicTable @register="registerTable" style="width: 96%">
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>
    <!-- 授权角色弹窗 -->
    <AssigningRoles @register="registerAssigningRolesModal" @success="initData" pageType="store" />
  </page-wrapper>
</template>
<script lang="ts" setup>
  import { ref, onMounted } from 'vue';
  import PageWrapper from '/@/components/Page/src/PageWrapper.vue';
  import { useModal } from '/@/components/Modal';
  import { BasicColumn, BasicTable, TableAction } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { fearchStoreRoleList } from '@/api/sys/store';
  import { AuthEnum } from '/@/enums/authEnum';
  import AssigningRoles from '../user/assigningRoles.vue';

  const { t } = useI18n('common');
  // 授权角色弹窗
  const [registerAssigningRolesModal, { openModal: openRoleHandleModal }] = useModal();
  const tableData = ref([
    {
      authLevel: t('dealerCustomerText'),
      roleName: '',
      roleId: [],
      id: 2,
    },
    {
      authLevel: t('storeText'),
      roleName: '',
      roleId: [],
      id: 3,
    },
  ] as any);
  const columns: BasicColumn[] = [
    {
      title: t('authLevel'),
      dataIndex: 'authLevel',
      width: 120,
    },
    {
      title: t('authRole'),
      dataIndex: 'roleName',
      width: 120,
    },
  ];
  // 列表页面公共参数、方法
  const { tableContext } = useListPage({
    designScope: 'StoreRoleAuth',
    tableProps: {
      dataSource: tableData,
      columns,
      useSearchForm: false,
      showTableSetting: false,
      showActionColumn: true,
      actionColumn: {
        title: t('operation'),
        width: 100,
      },
      pagination: false,
    },
  });
  const [registerTable] = tableContext;

  /**
   * 角色授权
   */
  async function handleAllotRole(record) {
    openRoleHandleModal(true, {
      authorizeLevel: record.id, // 登录获取的该用户等级
      roleIds: record.roleId, // 当前绑定的角色id
    });
  }

  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      // 綁定角色
      {
        label: t('roleAuth'),
        onClick: handleAllotRole.bind(null, record),
        auth: AuthEnum.STORE_ROLE_AUTH_SET,
      },
    ];
  }
  // 提取角色信息的方法
  const extractRoleData = (roles: any[] | undefined) => {
    if (!roles || roles.length === 0) return { ids: [], names: [] };
    const ids = roles.map((role) => role.roleId);
    const names = roles.map((role) => role.roleName);
    return { ids, names };
  };
  // 初始化数据
  const initData = async () => {
    const res = await fearchStoreRoleList({});
    // 处理总代（res[2]）
    const { ids: sideUserIds = [], names: sideUserNames } = extractRoleData(res[2]);
    // 处理门店（res[3]）
    const { ids: storeIds = [], names: storeNames } = extractRoleData(res[3]);

    // 更新 tableData
    tableData.value[0].roleName = sideUserNames.join(',');
    tableData.value[0].roleId = sideUserIds;
    tableData.value[1].roleName = storeNames.join(',');
    tableData.value[1].roleId = storeIds;
  };
  onMounted(() => {
    initData();
  });
</script>
