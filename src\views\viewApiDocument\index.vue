<template>
  <div class="pdf-container">
    <div v-for="(el, idx) in pageCount" :key="idx" class="pdf-page">
      <canvas :ref="setCanvasRef" class="pdf-canvas"></canvas>
      <div :ref="setTextLayerRef" class="textLayer"></div>
    </div>
  </div>
</template>

<script setup>
  import { onMounted, ref, nextTick } from 'vue';
  import { useLocaleStoreWithOut } from '/@/store/modules/locale';
  import { getDocument, GlobalWorkerOptions } from 'pdfjs-dist';
  import pdfjsWorker from 'pdfjs-dist/build/pdf.worker?worker';
  import { TextLayerBuilder } from '@/pdfjs/text_layer_builder';
  import '@/pdfjs/text_layer_builder.css';
  import * as pdfjsLib from 'pdfjs-dist/build/pdf';

  GlobalWorkerOptions.workerPort = new pdfjsWorker();

  const canvasRefs = ref([]); // 存储canvas DOM
  const textLayerRefs = ref([]); // 存储textLayer DOM
  const pageCount = ref(0);

  function setCanvasRef(el) {
    if (el) canvasRefs.value.push(el);
  }
  function setTextLayerRef(el) {
    if (el) textLayerRefs.value.push(el);
  }

  onMounted(async () => {
    const zn_url = '/apiWendang_zh-CN.pdf'; // PDF文件的URL或路径
    const other_url = '/apiWendang_other_cn.pdf'; // PDF文件的URL或路径
    const localeStore = useLocaleStoreWithOut();
    const loadingTask = getDocument(localeStore.getLocale == 'zh-CN' ? zn_url : other_url);
    loadingTask.promise.then(async (pdf) => {
      pageCount.value = pdf.numPages;
      // 等待DOM渲染
      await nextTick();

      // 渲染每一页
      for (let i = 1; i <= pdf.numPages; i++) {
        const page = await pdf.getPage(i);
        const viewport = page.getViewport({ scale: 1.5 });
        const canvas = canvasRefs.value[i - 1];
        const textLayerDiv = textLayerRefs.value[i - 1];
        if (!canvas || !textLayerDiv) continue;

        // 设定canvas宽高
        const targetWidth = 1000;
        const targetHeight = viewport.height;
        canvas.width = targetWidth;
        canvas.height = targetHeight;

        // 让canvas内容完全透明
        const canvasContext = canvas.getContext('2d');
        canvasContext.clearRect(0, 0, targetWidth, targetHeight);
        // 用自定义canvasFactory让pdf.js渲染到离屏canvas，再把透明像素绘制到主canvas
        const offscreen = document.createElement('canvas');
        offscreen.width = targetWidth;
        offscreen.height = targetHeight;
        const offCtx = offscreen.getContext('2d');
        const renderContext = {
          canvasContext: offCtx,
          viewport: viewport,
        };
        await page.render(renderContext).promise;
        // 把离屏canvas像素全部设为透明
        const imgData = offCtx.getImageData(0, 0, targetWidth, targetHeight);
        for (let i = 0; i < imgData.data.length; i += 4) {
          imgData.data[i + 3] = 0; // alpha通道设为0
        }
        canvasContext.putImageData(imgData, 0, 0);

        // 渲染文本层
        const textContent = await page.getTextContent();
        textLayerDiv.innerHTML = '';
        const textLayerBuilder = new TextLayerBuilder({
          textLayerDiv: textLayerDiv,
          pageIndex: i - 1,
          viewport: viewport,
          eventBus: null,
        });
        textLayerBuilder.setTextContent(textContent);
        textLayerBuilder.render();
        textLayerDiv.style.width = `${viewport.width}px`;
        textLayerDiv.style.height = `${viewport.height}px`;
        textLayerDiv.style.top = '0';
      }
    });
  });
</script>
<style>
  .pdf-container {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .pdf-page {
    position: relative;
  }
  .pdf-canvas {
    display: block;
    z-index: 0;
  }
  .textLayer {
    position: absolute;
    pointer-events: auto;
    user-select: text;
    width: 100%;
    height: 100%;
    z-index: 1;
  }
  .textLayer span {
    color: #000 !important;
    user-select: text;
    white-space: pre;
    line-height: 1;
    overflow: hidden;
    display: inline-block;
  }
</style>
