<template>
  <div v-loading="loading">
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" preIcon="ant-design:plus-outlined" @click.stop="handleCreate" v-auth="'sys:user:add'">
          {{ t('common.add') }}</a-button
        >
        <a-button type="primary" v-auth="'sys:user:import'" @click.stop="toDownloadTemplate"> {{ t('common.downloadTemplate') }}</a-button>
        <a-button type="primary" preIcon="ant-design:export-outlined" @click.stop="onExportXls" v-auth="'sys:user:import'">
          {{ t('common.export') }}</a-button
        >
        <j-upload-button
          type="primary"
          accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
          preIcon="ant-design:import-outlined"
          @click="onImportXls"
          v-auth="'sys:user:import'"
          >{{ t('common.import') }}</j-upload-button
        >
        <a-button type="primary" :disabled="isDisableDaijinhuo" @click.stop="batchJihuo" v-auth="'sys:user:batchJihuo'">
          {{ t('common.batch_be_activated') }}</a-button
        >
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)" />
      </template>
    </BasicTable>

    <!-- 禁用、启用变更原因 -->
    <a-modal v-model:open="statusEditOpen" :title="statusType === 1 ? t('common.enableText') : t('common.disableText')" @ok="statusEditOk">
      <div class="pt-50px pr-60px pb-102px pl-60px">
        <div style="font-size: 18px; color: #4b526a"
          >{{ statusType === 1 ? t('common.okTextEnable') : t('common.okTextDisable') }}{{ t('common.account_text') }}{{ editName }}</div
        >
        <div class="mt-20px" style="font-size: 14px; color: #4e5969">{{ t('common.change_reason') }}</div>
        <a-textarea
          class="mt-8px"
          style="border-radius: 2px"
          v-model:value="changeReason"
          :placeholder="t('common.Please_enter_change_reason')"
          :auto-size="{ minRows: 2, maxRows: 5 }"
        />
      </div>
    </a-modal>
  </div>
  <!-- 分配角色弹窗 -->
  <AssigningRoles @register="registerAssigningRolesModal" @success="reload" />
  <!-- 数据权限 -->
  <AssignCountrysHandleModal @register="AssignCountryHandleModal" @success="reload" />
</template>

<script lang="ts" name="system-user" setup>
  import { ref, onActivated, computed } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { BasicTable, TableAction, ActionItem } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useModal } from '/@/components/Modal';
  import { ObsTemporaryType, getObsTemporaryUrls } from '/@/api/obs';
  import { awaitTo } from '@ruqi/utils-admin';
  import { downloadExcelFile } from '/@/utils/file/download';
  import AssignCountrysHandleModal from './assignCountryHandleModal.vue';
  import AssigningRoles from './assigningRoles.vue';
  import { columns, searchFormSchema } from './user.data';
  import { addSjLogFn } from '/@/api/common/api';
  import { queryExportUrlApi, initUserApi, accountListApi, deleteUserApi, updateAccountStatusApi, getImportApi, getExportUrl } from './user.api';
  import { useI18n } from '/@/hooks/web/useI18n';

  const { t } = useI18n();

  const { createMessage, createConfirm } = useMessage();

  const loading = ref(false);

  // 审计日志当前操作页面-菜单
  const remarkLogStr = JSON.stringify(useRoute()?.meta?.originalTitles || []);

  let router = useRouter();

  // 启用、禁用弹窗相关配置
  const editId = ref('');
  const editName = ref('');
  const statusEditOpen = ref(false);
  const statusType = ref(1);
  const changeReason = ref('');

  // 新增、编辑角色弹窗
  const [registerAssigningRolesModal, { openModal: openRoleHandleModal }] = useModal();
  // 数据权限弹窗
  const [AssignCountryHandleModal, { openModal: openAssignCountryHandleModal }] = useModal();

  // 重新加载数据后是否需要充值勾选向
  function reloadInitSelection() {
    selectedRowKeys.value = [];
    selectedRows.value = [];
    reload();
  }

  // 批量激活
  const batchJihuo = async () => {
    loading.value = true;
    try {
      await initUserApi({
        userIds: selectedRows.value?.filter((fiterItems) => fiterItems.status === 3).map((mapItems) => mapItems.userId),
      });
      createMessage.success(t('common.jihuo_tip'));
      reloadInitSelection();
    } catch (err) {}
    loading.value = false;
  };

  // 列表页面公共参数、方法
  const { tableContext, onExportXls, onImportXls } = useListPage({
    designScope: 'user-list',
    tableProps: {
      rowKey: 'userId',
      api: accountListApi,
      columns: columns,
      size: 'small',
      clickToRowSelect: true,
      formConfig: {
        schemas: searchFormSchema,
        buttonLayout: 'right',
        layout: 'vertical',
      },
      actionColumn: {
        width: 150,
      },
      beforeFetch: (params) => {
        const [regionCode, country] = params.region_country?.split('____') || [];
        params = {
          ...params,
          regionCode: regionCode || '',
          country: country || '',
        };
        return params;
      },
    },
    exportConfig: {
      name: t('common.account_info_text'),
      methods: 'post',
      url: getExportUrl,
    },
    importConfig: {
      url: getImportApi,
      success: async (res) => {
        if (res.errors?.length) {
          let tips = res.errors.reduce((thisVal, nextParams) => {
            thisVal = thisVal + `${t('common.ordinalNumbers')}${nextParams.rowNumber}${t('common.row_text')}：${nextParams.errorMsg} \n`;
            return thisVal;
          }, '');
          tips = tips + t('common.please_edit_import_row');
          createConfirm({
            iconType: 'warning',
            title: t('common.app_logoutTip'),
            class: 'custom-modal',
            content: tips,
          });
          reload();
          return;
        }
        addSjLogFn(remarkLogStr, '导入');
        createMessage.success(t('common.importSuccess'));
        reloadInitSelection();
      },
      isSuccessCustomTip: true,
      fail: async () => {
        addSjLogFn(remarkLogStr, '导入');
      },
    },
  });

  // 下载模板-queryExportUrlApi
  async function toDownloadTemplate() {
    try {
      const res = await queryExportUrlApi();
      if (!res) {
        createMessage.error(t('common.file_download_fail'));
        return;
      }
      const [err, result] = await awaitTo(
        getObsTemporaryUrls({
          type: ObsTemporaryType.Download,
          filePath: res,
        })
      );
      if (err) {
        createMessage.error(t('common.file_download_fail'));
        return;
      }
      if (result.content) {
        try {
          downloadExcelFile(result.content, `${t('common.download_user_template')}.xlsx`);
        } catch (err) {
          createMessage.error(t('common.file_download_fail'));
        }
      }
    } catch (err) {}
  }

  //注册table数据
  const [registerTable, { reload }, { rowSelection, selectedRowKeys, selectedRows }] = tableContext;

  // 选中勾选的数据-是否存在待激活状态的数据-如果不存在禁用批量激活按钮
  const isDisableDaijinhuo = computed(() => {
    console.log(selectedRows);
    const findDaijihuo = selectedRows.value?.find((findItems) => findItems.status === 3);
    return !!findDaijihuo ? false : true;
  });

  /**
   * 新增事件
   */
  function handleCreate() {
    router.push({ path: '/system/user/add' });
  }
  /**
   * 编辑事件
   */
  async function handleEdit(record: Recordable) {
    router.push({ path: '/system/user/edit', query: { userId: record.userId } });
  }
  /**
   * 详情
   */
  async function handleDetail(record: Recordable) {
    router.push({ path: '/system/user/view', query: { userId: record.userId } });
  }
  /**
   * 分配角色
   */
  async function handleAllotRole(record: Recordable) {
    openRoleHandleModal(true, {
      userId: record.userId,
    });
  }

  /**
   * 删除事件
   */
  async function handleDelete(record) {
    deleteUserApi({ userId: record.userId }, reloadInitSelection);
  }

  // 禁用启用-确定操作
  async function statusEditOk() {
    if (!changeReason.value) {
      createMessage.warning(t('common.Please_enter_change_reason'));
      return;
    }
    try {
      await updateAccountStatusApi({ changeReason: changeReason.value, status: statusType.value, userId: editId.value });
      statusEditOpen.value = false;
      reload();
    } catch (err) {}
  }

  // 初始化账号
  async function toInitAccount(record) {
    loading.value = true;
    try {
      await initUserApi({
        userIds: [record.userId],
      });
      createMessage.success(t('common.account_activation_successful_text'));
      reloadInitSelection();
    } catch (err) {}
    loading.value = false;
  }

  /**
   * 操作栏
   */
  function getTableAction(record): ActionItem[] {
    return [
      {
        label: t('common.edit'),
        onClick: handleEdit.bind(null, record),
        auth: 'sys:user:edit',
        ifShow: () => ![5].includes(record.status),
      },
      {
        label: t('common.view'),
        onClick: handleDetail.bind(null, record),
        auth: 'sys:user:view',
        ifShow: () => [5].includes(record.status),
      },
    ];
  }
  /**
   * 下拉操作栏
   */
  function getDropDownAction(record): ActionItem[] {
    return [
      {
        label: t('common.view'),
        onClick: handleDetail.bind(null, record),
        auth: 'sys:user:view',
        ifShow: () => ![5].includes(record.status),
      },
      {
        label: t('common.assign_roles_text'),
        onClick: handleAllotRole.bind(null, record),
        auth: 'sys:role:roleAssign',
        ifShow: () => ![5].includes(record.status),
      },
      {
        label: t('common.delText'),
        onClick: handleDelete.bind(null, record),
        ifShow: () => [2, 0].includes(record.status),
        auth: 'sys:user:remove',
      },
      {
        label: t('common.enableText'),
        onClick: () => {
          editId.value = record.userId;
          editName.value = record.username;
          statusEditOpen.value = true;
          statusType.value = 1;
          changeReason.value = '';
        },
        ifShow: () => [2, 0].includes(record.status),
        auth: 'sys:user:updateStatus',
      },
      {
        label: t('common.disableText'),
        onClick: () => {
          editId.value = record.userId;
          editName.value = record.username;
          statusEditOpen.value = true;
          statusType.value = 2;
          changeReason.value = '';
        },
        ifShow: () => record.status === 1,
        auth: 'sys:user:updateStatus',
      },
      {
        label: t('common.activate_account_text'),
        onClick: toInitAccount.bind(null, record),
        ifShow: () => record.status === 3,
        auth: 'sys:user:init',
      },
      {
        label: t('common.assign_country_text'),
        onClick: () => {
          openAssignCountryHandleModal(true, {
            userId: record.userId,
          });
        },
        ifShow: record.accountCategory == 1,
        auth: 'sys:user:dataPermissions',
      },
    ];
  }

  onActivated(() => {
    reload();
  });
</script>

<style scoped></style>
