import * as pdfjsLib from 'pdfjs-dist/build/pdf';
// 该文件内容来自 PDF.js 官方 web/text_layer_builder.js，做了适配以便直接在项目中使用
// 仅保留核心 textLayerBuilder 相关代码
// 如需完整功能可参考 https://github.com/mozilla/pdf.js/blob/master/web/text_layer_builder.js

export class TextLayerBuilder {
  constructor({ textLayerDiv, pageIndex, viewport, eventBus }) {
    this.textLayerDiv = textLayerDiv;
    this.pageIndex = pageIndex;
    this.viewport = viewport;
    this.eventBus = eventBus;
    this.textContent = null;
    this.textDivs = [];
    this.renderingDone = false;
  }

  setTextContent(textContent) {
    this.textContent = textContent;
  }

  render() {
    if (!this.textContent) return;
    this.textDivs = [];
    this.textLayerDiv.innerHTML = '';
    const textItems = this.textContent.items;
    const viewport = this.viewport;
    for (let i = 0, len = textItems.length; i < len; i++) {
      const item = textItems[i];
      const textDiv = document.createElement('span');
      // 计算 transform
      const tx = pdfjsLib.Util.transform(viewport.transform, item.transform);
      const style = textDiv.style;
      style.left = tx[4] + 'px';
      style.top = tx[5] + 'px';
      style.fontSize = Math.sqrt(tx[2] * tx[2] + tx[3] * tx[3]) + 'px';
      style.fontFamily = item.fontName || 'sans-serif';
      style.position = 'absolute';
      style.whiteSpace = 'pre';
      style.color = 'rgba(0,0,0,0.01)';
      style.userSelect = 'text';
      style.lineHeight = '1';
      style.display = 'inline-block';
      textDiv.textContent = item.str;
      this.textLayerDiv.appendChild(textDiv);
      this.textDivs.push(textDiv);
    }
    this.renderingDone = true;
  }
}
