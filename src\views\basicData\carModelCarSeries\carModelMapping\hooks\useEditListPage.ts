import { computed, unref, nextTick, ref } from 'vue';
import { BasicTableProps, PaginationProps, TableActionType } from '/@/components/Table';
import { useListPage } from '/@/hooks/system/useListPage';

export type EditListPageProps = {
  tableProps: BasicTableProps;
  getInsertTableDataRecord?: (() => Recordable) | undefined;
};

/**
 * 编辑表格的hooks, 用于处理表格的编辑状态和分页
 * 提供以下功能：
 * 1. 管理表格的编辑状态，确保当前页的数据处于可编辑状态
 * 2. 处理分页切换时的数据提交和编辑状态切换
 * 3. 支持表格数据的插入、删除和设置
 * 4. 分页数据范围的计算和处理
 *
 * 主要方法：
 * - setTableData: 设置表格数据，并自动跳转到最后一页，使最新数据可见
 * - insertTableDataRecord: 插入表格记录，如当前页已满则自动跳转到下一页
 * - deleteTableDataRecord: 删除表格记录，并更新表格索引
 *
 * @param props tableProps - 表格属性配置，getInsertTableDataRecord - 获取插入记录的函数
 * @returns 注册表格的函数和编辑表格的方法
 */
export const useEditListPage = (props: EditListPageProps) => {
  const { tableProps } = props ?? {};

  const lastPageCurrent = ref(0); // 上一次的页码

  /**
   * 计算指定页面的数据范围（开始索引和结束索引）
   * 用于确定分页数据的边界，方便后续对特定页面数据的处理
   *
   * @param current 页码（从1开始）
   * @param pageSize 每页条数
   * @returns 包含开始索引start和结束索引end的对象
   */
  const getPageRange = (current: number, pageSize: number) => {
    const start = (current - 1) * pageSize;
    const end = start + pageSize;
    return { start, end };
  };

  /**
   * 处理指定范围内的数据
   * 对数据源中指定范围的记录执行统一操作，例如提交编辑、设置编辑状态等
   *
   * @param dataSource 数据源数组
   * @param start 开始索引
   * @param end 结束索引
   * @param action 对每条记录执行的处理函数
   */
  const processPageData = (dataSource: Recordable[], start: number, end: number, action: (item: Recordable) => void) => {
    dataSource?.slice(start, end).forEach(action);
  };

  /**
   * 导航到指定页面并更新lastPageCurrent
   * 封装页面跳转逻辑，确保分页状态的一致性
   *
   * @param pageNumber 目标页码
   */
  const navigateToPage = (pageNumber: number) => {
    editTableMethods?.setPagination({
      current: pageNumber,
    });
    lastPageCurrent.value = pageNumber;
  };

  const mergeTableProps = (tableProps: BasicTableProps): BasicTableProps => {
    const { onChange, columns, pagination = { pageSize: 10, pageSizeOptions: ['10', '20'] }, ...restProps } = tableProps || {};

    const newColumns = columns?.map((column) => {
      return {
        ...column,
      };
    });

    const newPagination = pagination
      ? {
          ...(pagination as PaginationProps),
          showQuickJumper: false,
        }
      : false;

    return {
      columns: newColumns,
      pagination: newPagination,
      onChange: (...params) => {
        onChange?.(...params);
        const [pagination, _, __, { currentDataSource, action }] = params;
        if (action === 'paginate') {
          const { current, pageSize } = pagination;
          // 当分页切换时，需要提交上一页的数据
          if (lastPageCurrent.value !== pagination.current) {
            const { start, end } = getPageRange(lastPageCurrent.value, pageSize!);
            processPageData(currentDataSource, start, end, (item: Recordable) => {
              item?.onSubmitEdit?.();
            });
          }
          // 设置当前页的编辑状态
          const { start: currentStart, end: currentEnd } = getPageRange(current, pageSize!);
          processPageData(currentDataSource, currentStart, currentEnd, (record: Recordable) => {
            setEditable(record.index);
          });
          lastPageCurrent.value = current;
        }
      },
      ...restProps,
    };
  };

  /**
   * 设置指定索引的记录为可编辑状态
   * @param index 记录索引
   */
  const setEditable = (index: number) => {
    nextTick(() => {
      const item = editTableMethods?.getDataSource()?.[index];
      item?.onEdit?.(true);
    });
  };

  const _tableProps = mergeTableProps(tableProps);

  const { tableContext } = useListPage({
    tableProps: _tableProps,
  });

  const [registerEditTable, editTableMethods] = tableContext;

  const editTableDataData = computed(() => {
    return editTableMethods?.getDataSource() ?? [];
  });

  const paginationRef = computed<PaginationProps>(() => {
    const pagination = editTableMethods?.getPaginationRef();
    if (pagination) {
      return pagination as PaginationProps;
    }
    return {};
  });

  /**
   * 更新数据源的index
   * 确保每条记录都有正确的索引值，用于后续的查找和编辑操作
   * @param dataSource 数据源
   */
  const updateTableDataIndex = (dataSource: Recordable[]) => {
    dataSource.forEach((item, index) => {
      item.index = index;
    });
  };

  return {
    registerEditTable,
    editTableMethods: {
      ...editTableMethods,
      /**
       * 设置表格数据
       * 设置数据后自动跳转到最后一页并设置为可编辑状态
       * @param dataSource 表格数据源
       */
      setTableData: (dataSource: Recordable[]) => {
        editTableMethods?.setTableData(dataSource);
        if (dataSource.length > 0) {
          // 计算出要跳转的页面，设置当前页的数据为可编辑状态
          const { pageSize } = unref(paginationRef);
          const nextPageCurrent = Math.ceil(dataSource.length / pageSize!);
          // 设置目标页数据为可编辑状态
          const { start: nextPageStart, end: nextPageEnd } = getPageRange(nextPageCurrent, pageSize!);
          processPageData(dataSource, nextPageStart, nextPageEnd, (item) => {
            setEditable(item.index);
          });
          // 导航到目标页
          navigateToPage(nextPageCurrent);
        }
      },
      /**
       * 插入表格记录
       * 如果当前页已满，会自动跳转到下一页
       * @param record 要插入的记录
       */
      insertTableDataRecord: (record: Recordable) => {
        editTableMethods?.insertTableDataRecord(record);
        const index = editTableMethods?.getDataSource()?.length - 1;
        setEditable(index);
        const { current, pageSize } = unref(paginationRef);
        const dataSource = unref(editTableDataData);
        // 当前页的数据已经满了，需要切换到下一页
        if (dataSource.length > current! * pageSize!) {
          // 切换到下一页之前，需要把当前页的数据提交一下
          const { start, end } = getPageRange(current!, pageSize!);
          processPageData(dataSource, start, end, (item) => {
            item?.onSubmitEdit?.();
          });
          // 导航到下一页
          const nextPageCurrent = current! + 1;
          navigateToPage(nextPageCurrent);
        }
      },
      /**
       * 删除表格记录
       * 删除记录后更新索引并设置为可编辑状态, 如果当前页的数据删除完，触发跨页的情况，直接设置上一页为可编辑状态
       * @param rowKey 要删除的记录的键
       */
      deleteTableDataRecord: (rowKey) => {
        editTableMethods?.deleteTableDataRecord(rowKey);
        const { current, pageSize } = unref(paginationRef);
        const dataSource = unref(editTableDataData);
        updateTableDataIndex(dataSource); // 更新索引-用于后续的操作
        nextTick(() => {
          // 如果当前页的数据删除完，触发跨页的情况，直接设置上一页为可编辑状态
          if (dataSource.length % pageSize! === 0 && current! > 1) {
            const { start: prePageStart, end: prePageEnd } = getPageRange(current! - 1, pageSize!);
            processPageData(dataSource, prePageStart, prePageEnd, (item) => {
              setEditable(item.index);
            });
            navigateToPage(current! - 1);
            return;
          }
          // 设置最新的当前页为可编辑状态
          const { start: currentStart, end: currentEnd } = getPageRange(current!, pageSize!);
          processPageData(dataSource, currentStart, currentEnd, (item) => {
            setEditable(item.index);
          });
        });
      },
    } as TableActionType,
    editTableDataData,
  };
};
