# Tab组件数据优化方案

## 问题背景

当前 Tab 组件采用懒加载模式，只有用户切换到某个标签页时才会加载该标签页的组件实例。在用户直接点击提交按钮时，系统需要校验所有标签页的数据，但由于未加载过的标签页，其组件实例尚未创建，因此无法获取表单和表格的方法（如 `getFieldsValue`, `getDataSource` 等）。

## 核心问题

1. **数据完整性问题**：如何确保提交时包含所有标签页的数据，包括未访问的标签页
2. **校验问题**：如何校验未访问标签页的数据合法性
3. **新增与编辑场景差异**：新增页和编辑页处理未加载标签页的逻辑不同

## 解决思路

针对不同场景采用不同的处理策略：

1. **编辑模式**：
   - 保存接口返回的原始数据
   - 对于用户访问过的标签页，使用最新的组件数据
   - 对于未访问的标签页，使用原始数据

2. **新增模式**：
   - 新增页面创建的是全新数据，不需要考虑原始数据
   - 只考虑用户已访问过的标签页数据
   - 未访问的标签页视为用户不需要，提交时可使用空数据

## 技术实现

### 1. 功能增强

在 `useTabPaneConfigs` hook 中增加三个核心功能：

1. **存储原始数据**：保存后端返回的原始数据
   ```typescript
   const originalData = ref<{
     tabsData?: Record<
       string,
       {
         formData?: Recordable;
         tableData?: Recordable[];
         secondaryTableData?: Recordable[];
       }
     >;
   }>({
     tabsData: {},
   });
   ```

2. **校验所有标签页**：包括已加载和未加载的标签页
   ```typescript
   const validateAllTabs = async (validator: {
     formValidator?: (formData: Recordable) => boolean | Promise<boolean>;
     tableValidator?: (tableData: Recordable[]) => boolean | Promise<boolean>;
     secondaryTableValidator?: (secondaryData: Recordable[]) => boolean | Promise<boolean>;
   }) => {
     // 实现代码
   };
   ```

3. **获取所有标签页数据**：包括已加载和未加载的标签页
   ```typescript
   const getTabsData = () => {
     // 实现代码
   };
   ```

### 2. 区分页面类型

为 `useTabPaneConfigs` 添加页面类型参数，支持根据页面类型采用不同处理策略：

```typescript
const {
  // ...其他参数
  pageType = PageTypeEnum.ADD,
} = options;
```

### 3. 数据获取逻辑

```typescript
const getTabsData = () => {
  const result = [];
  
  for (const pane of panes.value) {
    const paneKey = pane.key;
    
    if (loadedTabs.value.has(paneKey)) {
      // 已加载的标签页，使用组件方法获取数据
      result.push({
        formData: pane.config.formMethods?.getFieldsValue() || {},
        tableData: pane.config.tableMethods?.getDataSource() || [],
        secondaryTableData: pane.config.secondaryTableMethods?.getDataSource() || [],
      });
    } else if (pageType === PageTypeEnum.EDIT && originalData.value.tabsData && originalData.value.tabsData[paneKey]) {
      // 编辑模式下，未加载的标签页，使用原始数据
      const tabData = originalData.value.tabsData[paneKey];
      result.push({
        formData: tabData.formData || {},
        tableData: tabData.tableData || {},
        secondaryTableData: tabData.secondaryTableData || {},
      });
    } else {
      // 新增模式下的未加载标签页或其他情况，使用空数据
      result.push({
        formData: {},
        tableData: [],
        secondaryTableData: [],
      });
    }
  }
  
  return result;
};
```

### 4. 校验逻辑

```typescript
const validateAllTabs = async (validator) => {
  for (const pane of panes.value) {
    const paneKey = pane.key;
    
    if (loadedTabs.value.has(paneKey)) {
      // 校验已加载标签页
      // ...代码实现
    } else if (pageType === PageTypeEnum.EDIT && originalData.value.tabsData && originalData.value.tabsData[paneKey]) {
      // 编辑模式下，校验未加载标签页
      // ...代码实现
    } else if (pageType === PageTypeEnum.ADD) {
      // 新增模式下，跳过未加载标签页的校验
      continue;
    }
  }
  
  return true;
};
```

### 5. 标签页管理优化

为了保持数据一致性，在标签页删除时，同步清理原始数据：

```typescript
const removeTabData = (targetKey: string) => {
  if (originalData.value.tabsData && originalData.value.tabsData[targetKey]) {
    delete originalData.value.tabsData[targetKey];
  }
};
```

### 6. 代码优化与重构

为了进一步提高代码可维护性和减少重复逻辑，我们对代码进行了重构：

#### 6.1 定义标签页数据类型

```typescript
type TabData = {
  formData?: Recordable;
  tableData?: Recordable[];
  secondaryTableData?: Recordable[];
};
```

#### 6.2 定义标签页类型枚举

```typescript
enum TabTypeEnum {
  /** 已加载 */
  LOADED = 'LOADED',
  /** 未加载但有原始数据（编辑模式） */
  UNLOADED_WITH_ORIGINAL = 'UNLOADED_WITH_ORIGINAL',
  /** 未加载且无原始数据（新增模式） */
  UNLOADED_ADD_MODE = 'UNLOADED_ADD_MODE',
  /** 其他情况 */
  OTHER = 'OTHER',
}
```

#### 6.3 标签页类型判断函数

提取标签页类型判断逻辑为独立函数：

```typescript
const getTabType = (paneKey: string): TabTypeEnum => {
  const _originalData = originalData.value.tabsData?.[paneKey];
  
  if (loadedTabs.value.has(paneKey)) {
    return TabTypeEnum.LOADED;
  } else if (pageType === PageTypeEnum.EDIT && _originalData) {
    return TabTypeEnum.UNLOADED_WITH_ORIGINAL;
  } else if (pageType === PageTypeEnum.ADD && !loadedTabs.value.has(paneKey)) {
    return TabTypeEnum.UNLOADED_ADD_MODE;
  } else {
    return TabTypeEnum.OTHER;
  }
};
```

#### 6.4 标签页数据获取函数

根据标签页类型获取对应数据的通用函数：

```typescript
const getTabDataByType = (pane: PaneValueType, paneKey: string): TabData => {
  const tabType = getTabType(paneKey);
  const _originalData = originalData.value.tabsData?.[paneKey];
  
  switch (tabType) {
    case TabTypeEnum.LOADED:
      // 已加载标签页数据逻辑
      return {
        formData: pane.config.formMethods?.getFieldsValue() || {},
        tableData: pane.config.tableMethods?.getDataSource() || [],
        secondaryTableData: pane.config.secondaryTableMethods?.getDataSource() || [],
      };
    case TabTypeEnum.UNLOADED_WITH_ORIGINAL:
      // 编辑模式未加载标签页逻辑
      return {
        formData: _originalData?.formData || {},
        tableData: _originalData?.tableData || [],
        secondaryTableData: _originalData?.secondaryTableData || [],
      };
    case TabTypeEnum.UNLOADED_ADD_MODE:
    case TabTypeEnum.OTHER:
    default:
      // 新增模式未加载标签页或其他情况
      return {
        formData: {},
        tableData: [],
        secondaryTableData: [],
      };
  }
};
```

#### 6.5 优化后的数据获取逻辑

使用通用函数重构 `getTabsData`：

```typescript
const getTabsData = () => {
  const result: TabData[] = [];
  
  for (const pane of panes.value) {
    const paneKey = pane.key;
    const tabData = getTabDataByType(pane, paneKey); // 使用通用函数
    result.push(tabData);
  }
  
  return result;
};
```

#### 6.6 优化后的校验逻辑

使用标签页类型枚举重构 `validateAllTabs`：

```typescript
const validateAllTabs = async (validator: {/*...*/}) => {
  // ...
  
  for (const pane of panes.value) {
    const paneKey = pane.key;
    const tabType = getTabType(paneKey); // 获取标签页类型
    
    try {
      switch (tabType) {
        case TabTypeEnum.LOADED:
          // 校验已加载标签页
          // ...
          break;
          
        case TabTypeEnum.UNLOADED_WITH_ORIGINAL:
          // 校验编辑模式下未加载标签页
          // ...
          break;
          
        case TabTypeEnum.UNLOADED_ADD_MODE:
        case TabTypeEnum.OTHER:
          // 跳过校验
          continue;
      }
    } catch (error) {
      // ...
    }
  }
  
  return true;
};
```

## 使用方式

在组件中使用优化后的 hook：

```typescript
const { 
  activeKey, 
  panes, 
  addTabPane, 
  removeTab, 
  currentPane, 
  handleTabChange, 
  setOriginalData, 
  validateAllTabs, 
  getTabsData,
  clearOriginalData
} = useTabPaneConfigs({
  // ...其他配置
  pageType, // 传入页面类型
  lazyLoad: true,
  debug: true, // 是否启用调试日志
});

// 在获取详情数据时设置原始数据
const getDetail = async () => {
  // ...获取数据
  if (content.mtoConfigs) {
    const originalTabsData = {};
    
    _mtoConfigs.forEach((_config, idx) => {
      const tabKey = String(idx + 1);
      originalTabsData[tabKey] = {
        formData: { /* 表单数据 */ },
        tableData: [ /* 表格数据 */ ],
        secondaryTableData: [ /* 次表格数据 */ ],
      };
    });
    
    // 仅在编辑模式下设置原始数据
    pageType === PageTypeEnum.EDIT && setOriginalData(originalTabsData);
  }
};

// 校验所有配置
const validateAllConfig = async () => {
  return await validateAllTabs({
    formValidator: (formData) => {
      // 表单校验逻辑
    },
    tableValidator: (groups) => {
      // 表格校验逻辑
    },
    secondaryTableValidator: (mtocs) => {
      // 次表格校验逻辑
    }
  });
};

// 提交时获取所有数据
const handleSubmit = async () => {
  // 校验
  const result = await validateAllConfig();
  if (!result) return;
  
  // 获取所有标签页数据
  const allTabsData = getTabsData();
  
  // 处理数据并提交
  params.mtoConfigs = allTabsData.map((tabData) => {
    // 转换数据结构
  });
};
```

## 特殊场景处理

### 1. 动态添加/删除标签页

如果在编辑模式下添加或删除标签页，需确保原始数据与实际标签页保持同步。在删除标签页时，会同步删除对应的原始数据。

### 2. 部分加载的情况

当用户只访问了部分标签页时，系统会正确区分处理：
- 对已加载的标签页使用组件状态数据
- 对未加载的标签页使用原始数据（编辑模式）或空数据（新增模式）

### 3. 校验策略差异

- **编辑模式**：校验所有标签页（包括未访问的标签页）
- **新增模式**：只校验用户访问过的标签页，未访问的标签页视为用户不需要，跳过校验

## 优化效果

重构后的代码带来了以下好处：

1. **代码复用**：提取了共用逻辑，消除了重复的判断和处理
2. **可维护性提高**：逻辑更加清晰，职责分明
3. **可扩展性增强**：新增标签页类型只需修改少量代码
4. **逻辑一致性**：确保了相同类型的标签页在不同操作下处理一致
5. **可读性提升**：使用枚举和类型定义使代码意图更加明确

## 总结

本优化方案通过区分页面类型和标签页加载状态，解决了 Tab 懒加载模式下的数据完整性和校验问题，确保系统行为符合用户预期：

1. 编辑模式：保持数据完整性，未访问的标签页保留原始数据
2. 新增模式：只关注用户实际访问和填写的标签页
3. 优化了数据处理流程，统一了校验和提交的数据来源
4. 通过代码重构提高了可维护性和扩展性

该方案简化了组件逻辑，提高了代码可维护性，同时优化了用户体验。 