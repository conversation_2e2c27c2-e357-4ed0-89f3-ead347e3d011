<template>
  <a-card>
    <BasicForm @register="registerForm" @submit="handleSubmit" @cancel="handleCancel" />
  </a-card>
</template>

<script setup lang="ts">
  import { useForm, BasicForm, FormSchema } from '/@/components/Form';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { router } from '/@/router';
  import { useRoutePageType } from '/@/hooks/route';
  import { PageTypeEnum } from '/@/enums/common';
  import { onMounted, ref } from 'vue';
  import { awaitTo } from '@ruqi/utils-admin/esm/tools';
  import { featchCountryDetail, featchUpdateCountry, featchAddCountry, CountryTye } from '/@/api/basicData/nationCity';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { isEmpty, isUndefined } from 'lodash-es';
  import { useSessionStorageState } from 'vue-hooks-plus';
  import { useTabs } from '/@/hooks/web/useTabs';

  const { t } = useI18n('common');

  const pageType = useRoutePageType();

  const [nationInfo] = useSessionStorageState('NationItem', {
    defaultValue: {} as CountryTye,
  });

  const id = Number(nationInfo.value?.id);
  const disabled = pageType === PageTypeEnum.VIEW;

  const formSchemas: FormSchema[] = [
    {
      label: t('threeLetterCode'),
      field: 'iso3',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      required: true,
      componentProps: {
        placeholder: t('pleaseEnterContent'),
        disabled,
      },
    },
    {
      label: t('twoLetterCode'),
      field: 'iso2',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      required: true,
      componentProps: {
        placeholder: t('pleaseEnterContent'),
        disabled,
      },
    },
    {
      label: t('threeDigitCode'),
      field: 'numCode',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      required: true,
      componentProps: {
        placeholder: t('pleaseEnterContent'),
        disabled,
      },
    },
    {
      label: t('countryName'),
      field: 'cname',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      required: true,
      componentProps: {
        placeholder: t('pleaseEnterContent'),
        disabled,
      },
    },
  ];

  const submitButtonOptions = ref({ text: t('saveText'), preIcon: '', loading: false });

  const { close: closeTab } = useTabs();

  const [registerForm, { setFieldsValue }] = useForm({
    schemas: formSchemas,
    autoSubmitOnEnter: true,
    showResetButton: false,
    showCancelButton: true,
    cancelButtonOptions: { text: t(`${pageType === PageTypeEnum.VIEW ? 'back' : 'cancelText'}`), preIcon: '' },
    showSubmitButton: [PageTypeEnum.EDIT, PageTypeEnum.ADD].some((v) => v === pageType),
    submitButtonOptions,
    buttonLayout: 'left',
    labelAlign: 'left',
    layout: 'vertical',
  });

  /** 提交按钮配置 */
  const apiMap = {
    [PageTypeEnum.ADD]: featchAddCountry,
    [PageTypeEnum.EDIT]: featchUpdateCountry,
  };

  const dataDetail = ref({});

  const { createMessage } = useMessage();

  const handleSubmit = async (values: Record<string, any>) => {
    if (Reflect.ownKeys(values).length === 0) return;
    const api = apiMap[pageType];
    const params = values;
    if (pageType === PageTypeEnum.EDIT) {
      params.id = id;
    }
    submitButtonOptions.value.loading = true;
    const [err] = await awaitTo(api(params));
    if (!err) {
      createMessage.success(t('operationSuccess'));
      handleCancel();
    }
    submitButtonOptions.value.loading = false;
  };

  const getDetail = async () => {
    const [, content] = await awaitTo(featchCountryDetail({ id }));
    if (content) {
      dataDetail.value = content;
      const fieldsValue = Object.entries(dataDetail.value).reduce(
        (pre, [k, v]) => {
          if (isUndefined(v) || v === null) return pre;
          pre[k] = v;
          return pre;
        },
        {} as Record<string, any>
      );
      setFieldsValue(fieldsValue);
    }
  };

  const handleCancel = () => {
    closeTab();
    router.push('/basic-data/geographical-location/nation');
  };

  onMounted(async () => {
    if (pageType !== PageTypeEnum.ADD) {
      getDetail();
    }
  });
</script>

<style lang="less" scoped>
  ::v-deep(.ant-card-head) {
    border-bottom: none;
  }
</style>
