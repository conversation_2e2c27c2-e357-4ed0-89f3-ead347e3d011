import { computed } from 'vue';
import dayjs, { Dayjs } from 'dayjs';
import { FormState, CronRuleType, CronProps } from './useCronForm';
import {
  calculateTriggerDateByRule,
  convertDateBetweenTimezones,
  getUserSystemTimezone,
  SERVER_TIMEZONE,
  adjustMonthDaysForCrossDayTimezone,
  handleCrossDayTimezoneRules,
  adjustYearRuleForCrossDayTimezone,
} from './useCronDateUtils';
import { REPEAT_TYPE_ENUM } from '/@/enums/common';
import type { I18nGlobalTranslation } from '/@/hooks/web/useI18n';

/**
 * 使用Cron表达式
 * @param formState 表单状态
 * @param t 翻译函数
 * @param props 组件属性
 * @returns 计算Cron表达式、检查触发规则、触发规则验证结果、计算最小结束时间、解析Cron表达式
 */
export function useCronExpression(formState: FormState, t: I18nGlobalTranslation, props?: CronProps) {
  // 检查触发时间规则
  const checkTriggerRule = () => {
    // 不重复的情况，规则始终有效
    if (formState.repeatType === REPEAT_TYPE_ENUM.NO_REPEAT) {
      return { isValid: true };
    }

    // 小时规则无需特殊检查
    if (formState.repeatRuleType === 'hour') {
      return { isValid: true };
    }

    // 检查星期几选择（仅针对周规则）
    if (formState.repeatRuleType === 'week' && (!formState.weekDays || formState.weekDays.length === 0)) {
      return {
        isValid: false,
        message: t('pleaseSelectAtLeastOneWeek'),
      };
    }

    // 检查月份日期选择（仅针对月规则）
    if (formState.repeatRuleType === 'month' && (!formState.monthDays || formState.monthDays.length === 0)) {
      return {
        isValid: false,
        message: t('pleaseSelectAtLeastOneDate'),
      };
    }

    // 使用核心计算函数校验触发规则
    const result = calculateTriggerDateByRule(
      {
        repeatType: formState.repeatType === REPEAT_TYPE_ENUM.NO_REPEAT ? 'noRepeat' : 'repeat',
        repeatRuleType: formState.repeatRuleType,
        interval: formState.interval,
        weekDays: formState.weekDays,
        monthDays: formState.monthDays,
        startTime: formState.startTime,
        endTime: formState.endTime,
      },
      'validation',
      t
    );

    if (!result.isValid) {
      return {
        isValid: false,
        message: result.message,
      };
    }

    return { isValid: true };
  };

  // 触发规则验证结果
  const triggerRuleValidation = computed(() => {
    return checkTriggerRule();
  });

  // 计算cron表达式-生成
  const computedCronExpression = computed(() => {
    try {
      // 获取用户当前系统时区
      const userSystemTimezone = getUserSystemTimezone();

      if (formState.repeatType === REPEAT_TYPE_ENUM.NO_REPEAT) {
        // 不重复的cron表达式，秒默认为0
        const userDate = formState.startTime;

        // 将用户时区的时间转换为服务器时区的时间
        const serverDate = convertDateBetweenTimezones(userDate.clone(), userSystemTimezone, SERVER_TIMEZONE);

        // 使用服务器时区的时间生成表达式
        const minutes = serverDate.minute();
        const hours = serverDate.hour();
        const dayOfMonth = serverDate.date();
        const month = serverDate.month() + 1; // dayjs的月份从0开始
        const dayOfWeek = '?';
        const year = serverDate.year();

        return `0 ${minutes} ${hours} ${dayOfMonth} ${month} ${dayOfWeek} ${year}`;
      } else {
        // 重复的cron表达式，秒默认为0

        // 获取用户时区的日期和时间
        const userDate = formState.startTime.clone();

        // 将用户时区的时间转换为服务器时区的时间
        const serverDate = convertDateBetweenTimezones(userDate, userSystemTimezone, SERVER_TIMEZONE);

        // 使用服务器时区的时间生成表达式
        const minutes = serverDate.minute();
        const hours = serverDate.hour();
        let dayOfMonth = '*';
        let month = '*';
        let dayOfWeek = '?';
        let year = '*';

        switch (formState.repeatRuleType) {
          case 'hour':
            // 【测试功能】按小时重复，小时字段使用间隔，日期字段为'*'，星期字段为'?'
            const hourValue = formState.interval > 1 ? `0/${formState.interval}` : '*';
            dayOfMonth = '*';
            dayOfWeek = '?';

            return `0 ${minutes} ${hourValue} ${dayOfMonth} ${month} ${dayOfWeek} ${year}`;
          case 'day':
            // 按天重复，日期字段使用间隔，星期字段为'?'
            dayOfMonth = formState.interval > 1 ? `1/${formState.interval}` : '*';
            dayOfWeek = '?';
            break;
          case 'week':
            // 按周重复，日期字段为'?'，星期字段使用选定的星期几
            dayOfMonth = '?';

            // 使用cron表达式的星期几表示（1-7）
            let weekDaysForCron = [...formState.weekDays];

            // 使用通用函数处理跨天情况
            handleCrossDayTimezoneRules(userDate, serverDate, '周规则', (isAddOneDay) => {
              // 对每个选定的星期几进行调整
              const adjustedWeekDays = weekDaysForCron.map((dayValue) => {
                if (isAddOneDay) {
                  // 星期几加1，周六(7)需要特殊处理，变为周日(1)
                  const newValue = dayValue === 7 ? 1 : dayValue + 1;
                  console.debug(`调整星期几: ${dayValue} -> ${newValue} (加1)`);
                  return newValue;
                } else {
                  // 星期几减1，周日(1)需要特殊处理，变为周六(7)
                  const newValue = dayValue === 1 ? 7 : dayValue - 1;
                  console.debug(`调整星期几: ${dayValue} -> ${newValue} (减1)`);
                  return newValue;
                }
              });

              // 更新处理后的星期几值
              weekDaysForCron = adjustedWeekDays;
              console.debug(`调整后的星期几值: ${weekDaysForCron.join(',')}`);
            });

            // 排序并连接星期几值
            const weekDaysCopy = [...weekDaysForCron].sort((a, b) => a - b);
            dayOfWeek = weekDaysCopy.join(',');

            // 周规则下interval固定为1，无需额外处理
            break;
          case 'month':
            // 按月重复，日期字段使用选定的日期，星期字段为'?'
            dayOfWeek = '?';

            // 使用通用函数处理跨天情况
            handleCrossDayTimezoneRules(
              userDate,
              serverDate,
              '月规则',
              (isAddOneDay) => {
                // 调整选中的日期
                const adjustedMonthDays = adjustMonthDaysForCrossDayTimezone(formState.monthDays, isAddOneDay, serverDate);

                // 对调整后的日期排序
                if (adjustedMonthDays.length === 1) {
                  dayOfMonth = adjustedMonthDays[0].toString();
                } else {
                  const sortedDays = [...adjustedMonthDays].sort((a, b) => a - b);
                  dayOfMonth = sortedDays.join(',');
                }

                console.debug(`调整后的月份日期: ${dayOfMonth}`);
              },
              true // 生成表达式时为true
            );

            // 如果没有跨天，则使用原始日期
            if (dayOfMonth === '*') {
              if (formState.monthDays.length === 1) {
                dayOfMonth = formState.monthDays[0].toString();
              } else {
                // 处理多日期情况
                const monthDaysCopy = [...formState.monthDays].sort((a, b) => a - b);
                dayOfMonth = monthDaysCopy.join(',');
              }
            }

            // 处理月份间隔
            if (formState.interval > 1) {
              month = `*/${formState.interval}`;
            }
            break;
          case 'year':
            // 按年重复，使用具体的月和日，星期字段为'?'
            dayOfWeek = '?';

            // 常规处理：使用通用函数处理跨天情况
            handleCrossDayTimezoneRules(userDate, serverDate, '年规则', (isAddOneDay) => {
              // 使用辅助函数计算调整后的日期，同时传入用户时区日期
              const adjustment = adjustYearRuleForCrossDayTimezone(serverDate, isAddOneDay, userDate);

              // 设置月份和日期
              month = adjustment.month.toString();
              dayOfMonth = adjustment.day.toString();

              // 处理年份变化（如果有间隔）
              if (formState.interval > 1 && adjustment.yearOffset !== 0) {
                const startYear = serverDate.year() + adjustment.yearOffset;
                const endYear = formState.endTime.year() + adjustment.yearOffset;
                year = `${startYear}-${endYear}/${formState.interval}`;
                console.debug(
                  `年规则年份调整: 间隔=${formState.interval}, 年份范围 ${serverDate.year()}-${formState.endTime.year()} -> ${startYear}-${endYear}`
                );
              } else if (adjustment.yearOffset !== 0) {
                // 年份有变化但无间隔，使用通配符
                year = '*';
              }

              // 打印调试信息
              console.debug(`年规则跨天调整: ${serverDate.format('M月D日')} -> ${month}月${dayOfMonth}日 (年份偏移: ${adjustment.yearOffset})`);
            });

            // 如果没有跨天调整，使用原始日期
            if (dayOfMonth === '*') {
              dayOfMonth = serverDate.date().toString();
              month = (serverDate.month() + 1).toString(); // dayjs的月份从0开始
            }

            // 年的间隔处理（只有在没有特殊处理时才应用）
            if (formState.interval > 1 && year === '*') {
              year = `${serverDate.year()}-${formState.endTime.year()}/${formState.interval}`;
            }
            break;
        }

        return `0 ${minutes} ${hours} ${dayOfMonth} ${month} ${dayOfWeek} ${year}`;
      }
    } catch (error) {
      console.error('生成Cron表达式错误:', error);
      return '';
    }
  });

  // 计算最小结束时间
  const calculateMinEndTime = () => {
    // 使用核心计算函数获取最小结束时间
    const result = calculateTriggerDateByRule(
      {
        repeatType: formState.repeatType === REPEAT_TYPE_ENUM.NO_REPEAT ? 'noRepeat' : 'repeat',
        repeatRuleType: formState.repeatRuleType,
        interval: formState.interval,
        weekDays: formState.weekDays,
        monthDays: formState.monthDays,
        startTime: formState.startTime,
        endTime: formState.endTime,
      },
      'minEnd',
      t
    );

    return result.date || formState.startTime.clone().add(1, 'day');
  };

  /**
   * 解析Cron表达式并设置表单状态-回显
   * 格式: 秒 分钟 小时 日期 月份 星期 年份
   */
  const parseCronExpression = (cron: string) => {
    if (!cron) return;

    try {
      const [_, minute, hour, dayOfMonth, month, dayOfWeek, year] = cron.split(' ');

      // 获取用户当前系统时区
      const userSystemTimezone = getUserSystemTimezone();

      // 解析基本时间
      let repeatType = REPEAT_TYPE_ENUM.NO_REPEAT;
      let repeatRuleType = 'day' as CronRuleType;
      let interval = 1;
      let weekDays = [1];
      let monthDays = [1];

      // 首先，让我们从服务器时区表达式创建一个基准日期
      let serverTime: Dayjs;

      // 判断是否为不重复的cron表达式 (特定年份)
      if (year && year !== '*') {
        // 不重复的情况
        // 从表达式创建精确的服务器时区时间
        serverTime = dayjs()
          .year(parseInt(year))
          .month(parseInt(month) - 1)
          .date(parseInt(dayOfMonth))
          .hour(parseInt(hour))
          .minute(parseInt(minute))
          .second(0);

        // 非重复的情况下，重复类型为不重复
        repeatType = REPEAT_TYPE_ENUM.NO_REPEAT;
      } else {
        // 重复的情况
        repeatType = REPEAT_TYPE_ENUM.REPEAT;

        // 解析重复规则
        if (dayOfMonth !== '*' && dayOfMonth !== '?') {
          if (dayOfMonth.includes('/')) {
            // 按天重复 1/N
            repeatRuleType = 'day';
            interval = parseInt(dayOfMonth.split('/')[1]);
          } else if (dayOfMonth.includes(',')) {
            // 按月重复的特定日期
            // 暂时设置为月规则，后续可能会根据month值覆盖为年规则
            repeatRuleType = 'month';
            monthDays = dayOfMonth.split(',').map((d) => parseInt(d));
          } else if (!isNaN(parseInt(dayOfMonth))) {
            // 按月单日重复或按年重复的特定日期
            // 暂时设置为月规则，后续可能会根据month值覆盖为年规则
            repeatRuleType = 'month';
            monthDays = [parseInt(dayOfMonth)];
          }
        }

        // 检查小时是否有间隔模式
        if (hour === '*' || hour.includes('/')) {
          // 按小时重复: 如 0/N 或 *
          repeatRuleType = 'hour';
          interval = hour.includes('/') ? parseInt(hour.split('/')[1]) : 1;
          console.debug(`检测到小时规则: ${hour}, 设置间隔=${interval}`);
        }

        if (dayOfWeek !== '*' && dayOfWeek !== '?') {
          // 按周重复
          repeatRuleType = 'week';
          // Cron表达式中星期几的值是1-7（1=周日，2=周一...7=周六）
          // 直接使用Cron表达式的值，无需转换
          weekDays = dayOfWeek.split(',').map((d) => parseInt(d));

          // 调试信息
          console.debug(`CRON周几值: ${dayOfWeek}, 内部使用相同的值: ${weekDays.join(',')}`);

          // 示例: cron="0 0 18 ? * 3,4 *"中3,4代表周二和周三

          // 周规则下，interval固定为1
          interval = 1;
        }

        // 处理月份间隔
        if (month.includes('/')) {
          repeatRuleType = 'month';
          interval = parseInt(month.split('/')[1]);
        }
        // 检查月份是否是具体值（非通配符和间隔）
        else if (month !== '*') {
          // 如果月份是具体值，可能是年规则
          // 例如 "0 30 0 1 1 ? *" 表示每年1月1日00:30执行
          const specificMonth = parseInt(month);
          if (!isNaN(specificMonth)) {
            // 确认是年规则的条件：月份和日期都是具体值
            if (dayOfMonth !== '*' && dayOfMonth !== '?' && !dayOfMonth.includes('/')) {
              console.debug(`检测到年规则特征：具体月份 ${month} 和具体日期 ${dayOfMonth}`);
              repeatRuleType = 'year';
              // 年规则下，间隔默认为1（每年）
              interval = 1;
            }
          }
        }

        // 处理年份间隔
        if (year && year.includes('/')) {
          repeatRuleType = 'year';
          interval = parseInt(year.split('/')[1]);
        }

        // 使用优化的逻辑创建基准时间:
        // 对于重复任务，必须依赖originalStartTime，因为Cron表达式不包含具体日期信息
        // 对于一次性任务，不需要originalStartTime，因为年份、月份和日期已经包含在表达式中
        if (props?.originalStartTime) {
          // 使用传入的原始开始时间作为基准，但只保留日期部分，时间完全由cron表达式决定
          // 这样可以确保时区转换后显示正确的时间
          const originalDate = dayjs(props.originalStartTime);

          // 记录原始信息，便于调试
          console.debug(`原始开始时间: ${props.originalStartTime}, 解析后: ${originalDate.format('YYYY-MM-DD HH:mm:ss')}`);
          console.debug(`Cron时间: ${hour}:${minute}`);

          // 从原始日期中提取年月日，结合cron表达式中的时分秒
          // 重要：这样处理确保即使在非整点时区（如UTC+5:30）下，时间显示也正确
          // 修复：增加对小时字段为通配符*的处理
          const hourValue = hour === '*' ? originalDate.hour() : parseInt(hour);
          console.debug(`小时字段处理：${hour} -> ${hourValue}`);
          serverTime = dayjs()
            .year(originalDate.year())
            .month(originalDate.month())
            .date(originalDate.date())
            .hour(hourValue)
            .minute(parseInt(minute))
            .second(originalDate.second());

          console.debug(`服务器时间(调整后): ${serverTime.format('YYYY-MM-DD HH:mm:ss')}`);
        } else {
          // 如果没有提供原始开始时间，则使用当前时间作为临时替代
          // 警告：对于重复任务，这可能导致显示不一致
          console.warn('缺少原始开始时间(originalStartTime)，重复任务可能无法正确回显日期。');
          const now = dayjs();
          serverTime = now.hour(parseInt(hour)).minute(parseInt(minute)).second(0);
        }
      }

      // 将服务器时区时间转换为用户当前系统时区
      const startTime = convertDateBetweenTimezones(serverTime, SERVER_TIMEZONE, userSystemTimezone);

      // 转换后的时间已经包含了正确的日期和时间，无需再进行小时调整
      // 只需确保秒数为0（cron表达式不包含秒）
      const startTimeAdjusted = startTime.second(0);

      // 记录调试信息
      console.debug(`服务器时区时间: ${serverTime.format('YYYY-MM-DD HH:mm:ss')}`);
      console.debug(`用户时区时间(转换后): ${startTime.format('YYYY-MM-DD HH:mm:ss')}`);
      console.debug(`最终调整时间: ${startTimeAdjusted.format('YYYY-MM-DD HH:mm:ss')}`);

      // 如果是周规则，处理跨天导致的星期几调整
      if (repeatRuleType === 'week' && weekDays.length > 0) {
        // 使用通用函数处理跨天情况
        handleCrossDayTimezoneRules(
          startTime,
          serverTime,
          '周规则回显',
          (isAddOneDay) => {
            // 对每个星期几进行调整
            const adjustedWeekDays = weekDays.map((dayValue) => {
              if (isAddOneDay) {
                // 星期几加1，周六(7)需要特殊处理，变为周日(1)
                const newValue = dayValue === 7 ? 1 : dayValue + 1;
                console.debug(`回显调整星期几: ${dayValue} -> ${newValue} (加1)`);
                return newValue;
              } else {
                // 星期几减1，周日(1)需要特殊处理，变为周六(7)
                const newValue = dayValue === 1 ? 7 : dayValue - 1;
                console.debug(`回显调整星期几: ${dayValue} -> ${newValue} (减1)`);
                return newValue;
              }
            });

            // 更新处理后的星期几值
            weekDays = adjustedWeekDays;
            console.debug(`回显调整后的星期几值: ${weekDays.join(',')}`);
          },
          true // 解析表达式时为true
        );
      }

      // 如果是月规则，处理跨天导致的日期调整
      if (repeatRuleType === 'month' && monthDays.length > 0) {
        // 使用通用函数处理跨天情况
        handleCrossDayTimezoneRules(
          startTime,
          serverTime,
          '月规则回显',
          (isAddOneDay) => {
            // 调整日期
            monthDays = adjustMonthDaysForCrossDayTimezone(monthDays, isAddOneDay, startTime);
            console.debug(`回显调整后的月份日期: ${monthDays.join(',')}`);
          },
          true // 解析表达式时为true
        );
      }

      // 如果是年规则，处理跨天导致的日期调整
      if (repeatRuleType === 'year') {
        // 使用通用函数处理跨天情况
        let needsAdjustment = false;
        let adjustment;

        handleCrossDayTimezoneRules(
          startTime,
          serverTime,
          '年规则回显',
          (isAddOneDay) => {
            // 记录需要调整
            needsAdjustment = true;

            // 使用辅助函数计算调整后的日期
            // 同时传入用户时区日期startTime，确保可以特殊处理12月31日的情况
            adjustment = adjustYearRuleForCrossDayTimezone(startTime, isAddOneDay, startTime);

            // 打印调试信息
            console.debug(
              `年规则回显调整: ${startTime.format('M月D日')} -> ${adjustment.month}月${adjustment.day}日 (年份偏移: ${adjustment.yearOffset})`
            );
          },
          true // 解析时为true
        );

        // 如果需要调整，更新表单状态中的日期信息
        if (needsAdjustment && formState && adjustment) {
          console.debug(`年规则回显调整前的时间: ${formState.startTime.format('YYYY-MM-DD HH:mm:ss')}`);

          // 使用克隆的时间对象进行调整
          let adjustedStartTime = formState.startTime
            .clone()
            .month(adjustment.month - 1) // 月份从0开始
            .date(adjustment.day);

          // 处理年份变化
          if (adjustment.yearOffset !== 0) {
            const newYear = formState.startTime.year() + adjustment.yearOffset;
            adjustedStartTime = adjustedStartTime.year(newYear);
            console.debug(`年规则年份调整: ${formState.startTime.year()} -> ${newYear}`);
          }

          // 覆盖表单状态的时间
          formState.startTime = adjustedStartTime;

          console.debug(`年规则回显调整后的时间: ${formState.startTime.format('YYYY-MM-DD HH:mm:ss')}`);
        }
      }

      // 设置结束时间 (优先使用props中的endTime)
      let endTime: Dayjs;
      if (props?.endTime) {
        // 如果传入了endTime，使用传入的时间并转换为用户时区
        endTime = dayjs(props.endTime);

        // 记录原始结束时间
        console.debug(`原始结束时间: ${props.endTime}, 解析后: ${endTime.format('YYYY-MM-DD HH:mm:ss')}`);

        // 转换时区
        endTime = convertDateBetweenTimezones(endTime, SERVER_TIMEZONE, userSystemTimezone);

        console.debug(`结束时间(转换后): ${endTime.format('YYYY-MM-DD HH:mm:ss')}`);
      } else if (repeatType === REPEAT_TYPE_ENUM.NO_REPEAT) {
        endTime = startTime.clone().add(1, 'day');
      } else {
        // 重复性任务：根据规则类型计算结束时间
        switch (repeatRuleType) {
          case 'hour':
            // 【测试功能】小时规则结束时间设置
            endTime = startTime.clone().add(interval * 24, 'hour');
            break;
          case 'day':
            endTime = startTime.clone().add(interval * 7, 'day');
            break;
          case 'week':
            endTime = startTime.clone().add(interval * 4, 'week');
            break;
          case 'month':
            endTime = startTime.clone().add(interval * 3, 'month');
            break;
          case 'year':
            endTime = startTime.clone().add(interval, 'year');
            break;
        }
      }

      // 更新表单状态
      Object.assign(formState, {
        repeatType,
        startTime: startTimeAdjusted,
        endTime,
        repeatRuleType,
        interval,
        weekDays,
        monthDays,
      });
    } catch (error) {
      console.error('解析Cron表达式错误:', error);
    }
  };

  return {
    computedCronExpression,
    checkTriggerRule,
    triggerRuleValidation,
    calculateMinEndTime,
    parseCronExpression,
  };
}
