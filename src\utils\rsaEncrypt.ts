import { JSEncrypt } from 'jsencrypt';

// RSA加密
class RSAEncryptor {
  private encryptor: JSEncrypt;
  private static readonly publicKey: string = `MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEArzdmP59VZhP0nAD3dZXCG90o9uDOabs0FG4Iyqe/nqBEffE4ycDZV5Sj6L6QsWDIKHvsxVNkTSSmeg2h5vnNrraxb/DmM78XptqhMKzEUujHHPnTwFqyU6dVxlKUZt7tQrbf9/vddtBYl7EMgIlUWJxmdHJG1J/IGT1+WtSwcjI2261uxJ0A12lpW5MMLghlXKjtINjf2YfLYQX+BnLBIUAjxyHnAERbXfTr9FSUJuDi86FOyXRMRE0abOqAadiXauAxmZp9GevXP+l/etum07TkSczsNFyO7ORqZ/bf4unV63OP2Fp+d6ydiJ0u9whjke1z8oH7t34SLK+xd+3YoQIDAQAB`;

  constructor() {
    this.encryptor = new JSEncrypt();
    this.encryptor.setPublicKey(RSAEncryptor.publicKey);
  }

  public encrypt(data: string): string | false {
    return this.encryptor.encrypt(data);
  }
}

const rsaEncryptor = new RSAEncryptor();
export default rsaEncryptor;
