<template>
  <a-card>
    <BasicForm @register="registerForm" @cancel="handleCancel">
      <!-- 车辆信息 -->
      <template #carInfoTitle="{ field }">
        <h3>{{ t(`${field}`) }}</h3>
      </template>
      <!-- 经销商信息 -->
      <template #dealerInfoTitle="{ field }">
        <h3>{{ t(`${field}`) }}</h3>
      </template>
      <!-- 用户信息 -->
      <template #userInfoTitle="{ field }">
        <h3>{{ t(`${field}`) }}</h3>
      </template>
      <!-- 车辆检查与保养信息 -->
      <template #cimInfoTitle="{ field }">
        <h3>{{ t(`${field}`) }}</h3>
      </template>
      <!-- 其他信息 -->
      <template #otherInfoTitle="{ field }">
        <h3>{{ t(`${field}`) }}</h3>
      </template>
    </BasicForm>
  </a-card>
</template>

<script setup lang="ts">
  import { onMounted } from 'vue';
  import { useRoute } from 'vue-router';
  import { router } from '/@/router';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useForm, BasicForm, FormSchema } from '/@/components/Form';
  import { awaitTo } from '@ruqi/utils-admin';
  import { useTabs } from '/@/hooks/web/useTabs';
  import { featchSalesData } from '@/api/userCenter';
  import { IsModifyStatusOptions } from '@/enums/common';
  import {
    SaleChannelOptions,
    CustomerTypeOptions,
    GenderTypeOptions,
    MaritalStatusOptions,
    PaymentMethodOptions,
    CustomerAgeOptions,
  } from '@/enums/userEnum';
  import { stringDesensitization, stringDesensitization2 } from '/@/utils/validator';

  const { t } = useI18n('common');
  const route = useRoute(); // 获取当前路由对象
  const { close: closeTab } = useTabs();

  const vin: string = Array.isArray(route.params.id) ? route.params.id[0] : route.params.id;

  const formSchemas: FormSchema[] = [
    {
      label: '',
      field: 'carInfo',
      slot: 'carInfoTitle',
      component: 'Input',
    },
    {
      label: t('vehicleVINCode'),
      field: 'vin',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
      },
    },
    {
      label: t('vehicleModelCode'),
      field: 'vehModelCode',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
      },
    },
    {
      label: t('vehicleSeriesCode'),
      field: 'seriesCode',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
      },
    },
    {
      label: t('configCode'),
      field: 'packageCode',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
      },
    },
    {
      label: t('colorCode'),
      field: 'colorCode',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
      },
    },
    {
      label: t('brand'),
      field: 'brand',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
      },
    },
    {
      label: t('material'),
      field: 'material',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
      },
    },
    {
      label: t('productCode'),
      field: 'productCode',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
      },
    },
    {
      label: t('carSaleDate'),
      field: 'saleDate',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
      },
    },
    {
      label: t('saleOrderNo'),
      field: 'saleNo',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
      },
    },
    {
      label: t('isGiveChargingPile'),
      field: 'isGiveChargingPile',
      component: 'Select',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
        options: IsModifyStatusOptions,
      },
    },
    {
      label: t('suggestPrice'),
      field: 'suggestPrice',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
      },
    },
    {
      label: t('terminalPrice'),
      field: 'terminalPrice',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
      },
    },
    {
      label: t('currencyType'),
      field: 'currencyType',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
      },
    },
    {
      label: t('salesChannels'),
      field: 'salesChannels',
      component: 'Select',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
        options: SaleChannelOptions,
      },
    },
    {
      label: '',
      field: 'dealerInfo',
      slot: 'dealerInfoTitle',
      component: 'Input',
    },
    {
      label: t('regionalCode'),
      field: 'region',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
      },
    },
    {
      label: t('agentCode'),
      field: 'dealerCode',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
      },
    },
    {
      label: t('dealerName'),
      field: 'dealerName',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
      },
    },
    {
      label: t('carBuyShop'),
      field: 'storeName',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
      },
    },
    {
      label: '',
      field: 'userInfo',
      slot: 'userInfoTitle',
      component: 'Input',
    },
    {
      label: t('name'),
      field: 'userName',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
      },
    },
    {
      label: t('nickName'),
      field: 'nickName',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
      },
    },
    {
      label: t('gender'),
      field: 'gender',
      component: 'Select',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
        options: GenderTypeOptions,
      },
    },
    {
      label: t('birthday'),
      field: 'birthDate',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
      },
    },
    {
      label: t('countryText'),
      field: 'countryName',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
      },
    },
    {
      label: t('introduce'),
      field: 'introduction',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
      },
    },
    {
      label: t('nationCode'),
      field: 'nationCode',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
      },
    },
    {
      label: t('phoneNum'),
      field: 'phone',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
      },
    },
    {
      label: t('email'),
      field: 'email',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
      },
    },
    {
      label: t('contactNumber'),
      field: 'contactNumber',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
      },
    },
    {
      label: t('fixedLine'),
      field: 'fixedLine',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
      },
    },
    {
      label: t('fax'),
      field: 'fax',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
      },
    },
    {
      label: t('area_text'),
      field: 'area',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
      },
    },
    {
      label: t('address'),
      field: 'address',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
      },
    },
    {
      label: t('postCode'),
      field: 'postCode',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
      },
    },
    {
      label: t('clueAddress'),
      field: 'clueAddress',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
      },
    },
    {
      label: t('firstTimeBuyCar'),
      field: 'firstTimeBuyCar',
      component: 'Select',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
        options: IsModifyStatusOptions,
      },
    },
    {
      label: t('customerType'),
      field: 'customerType',
      component: 'Select',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
        options: CustomerTypeOptions,
      },
    },
    {
      label: t('company_name_text'),
      field: 'companyName',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
      },
    },
    {
      label: t('companyPhone'),
      field: 'companyPhone',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
      },
    },
    {
      label: t('customerAge'),
      field: 'ageType',
      component: 'Select',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
        options: CustomerAgeOptions,
      },
    },
    {
      label: t('maritalStatus'),
      field: 'maritalStatus',
      component: 'Select',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
        options: MaritalStatusOptions,
      },
    },
    {
      label: t('childNum'),
      field: 'childNum',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
      },
    },
    {
      label: t('salary'),
      field: 'salary',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
      },
    },
    {
      label: '',
      field: 'cimInfo',
      slot: 'cimInfoTitle',
      component: 'Input',
    },
    {
      label: t('paymentType'),
      field: 'paymentType',
      component: 'Select',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
        options: PaymentMethodOptions,
      },
    },
    {
      label: t('invoiceDate'),
      field: 'invoiceDate',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
      },
    },
    {
      label: t('pdiDate'),
      field: 'pdiDate',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
      },
    },
    {
      label: '',
      field: 'otherInfo',
      slot: 'otherInfoTitle',
      component: 'Input',
    },
    {
      label: t('maintainExpireTime'),
      field: 'maintainExpireTime',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
      },
    },
    {
      label: t('threeElectExpireTime'),
      field: 'threeElectExpireTime',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
      },
    },
    {
      label: t('langCode'),
      field: 'lang',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
      },
    },
    {
      label: t('Logintoken'),
      field: 'token',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
      },
    },
    {
      label: t('remark'),
      field: 'remark',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
      },
    },
    {
      label: t('attachmentAddress'),
      field: 'attachmentAddress',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
      },
    },
    {
      label: t('fcmToken'),
      field: 'deviceToken',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
      },
    },
    {
      label: t('vehiclePlateNumber'),
      field: 'plateNumber',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
      },
    },
    {
      label: t('salesName'),
      field: 'salesName',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      componentProps: {
        disabled: true,
        placeholder: ' ',
      },
    },
  ];

  const [registerForm, { setFieldsValue }] = useForm({
    schemas: formSchemas,
    autoSubmitOnEnter: true,
    showResetButton: false,
    showCancelButton: true,
    showSubmitButton: false,
    cancelButtonOptions: { text: t('back'), preIcon: '' },
    buttonLayout: 'left',
    labelAlign: 'left',
    layout: 'vertical',
  });

  const getDetail = async () => {
    const [, content] = await awaitTo(featchSalesData({ vin }));
    if (content) {
      const data: any = {
        ...content,
      };
      setFieldsValue({
        ...data,
        vin: data.vin ? stringDesensitization(data.vin, 7) : ' ',
        phone: data.phone ? stringDesensitization(data.phone, 7) : ' ',
        email: data.email ? stringDesensitization(data.email, 8) : ' ',
        contactNumber: data.contactNumber ? stringDesensitization(data.contactNumber, 7) : ' ',
        fixedLine: data.fixedLine ? stringDesensitization(data.fixedLine, 7) : ' ',
        fax: data.fax ? stringDesensitization(data.fax, 7) : ' ',
        address: data.address ? stringDesensitization2(data.address, 4) : ' ',
        clueAddress: data.clueAddress ? stringDesensitization2(data.clueAddress, 4) : ' ',
        companyPhone: data.companyPhone ? stringDesensitization(data.companyPhone, 7) : ' ',
        plateNumber: data.plateNumber ? stringDesensitization(data.plateNumber, 5) : ' ',
      });
    }
  };
  const handleCancel = () => {
    closeTab();
    router.push('/user-center/vehicle-bind');
  };

  onMounted(async () => {
    getDetail();
  });
</script>

<style lang="less" scoped>
  ::v-deep(.ant-card-head) {
    border-bottom: none;
  }
  h3 {
    margin: 10px 0 -10px 0;
  }
</style>
