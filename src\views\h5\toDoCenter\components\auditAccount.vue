<template>
  <div class="audit_account" style="margin-top: 20px">
    <Empty :text="t('noResignAccountCancellationAudit')" v-if="invitationAccountList.length === 0" />
    <div v-else class="invitation_link_list">
      <div class="invitation_link_list_item" v-for="item in invitationAccountList" :key="item.id" @click="handleAuditAccount(item)">
        <div class="item">
          <div class="item_title">{{ t('name') }}：</div>
          <div class="item_content">{{ item.userName }}</div>
        </div>
        <div class="item">
          <div class="item_title">{{ t('network_text') }}：</div>
          <div class="item_content" v-if="item.accountCategory == '2'">{{ item.customerName ? item.customerName : '' }}</div>
          <div class="item_content" v-else-if="item.accountCategory == '3'">{{ item.networkName ? item.networkName : '' }}</div>
        </div>
        <div class="item">
          <div class="item_title">{{ t('role_text') }}：</div>
          <div class="item_content">{{ item.roleName }}</div>
        </div>
        <div class="item">
          <div class="item_title">{{ t('status') }}：</div>
          <div class="item_content" :style="{ color: item.statusColor }">{{ item.statusName }}</div>
        </div>
        <div class="item" v-if="[2, 3].includes(item.status)">
          <div class="item_title">{{ t('auditTime') }}：</div>
          <div class="item_content">{{ item.approvalTime ? dayjs(item.approvalTime).format('YYYY-MM-DD HH:mm') : '' }}</div>
        </div>
        <div class="item" v-if="[2, 3].includes(item.status)">
          <div class="item_title">{{ t('auditResultRemark') }}：</div>
          <div class="item_content">{{ item.auditRemark }}</div>
        </div>
        <div class="look_detail">
          <Icon name="arrow" />
        </div>
      </div>
    </div>
  </div>

  <!-- 注销申请审核 -->
  <Popup
    :show="showApplyAuditAccount"
    closeable
    position="center"
    :style="{ width: '86%', height: '50%' }"
    @click-overlay="showApplyAuditAccount = false"
    @close="showApplyAuditAccount = false"
  >
    <div class="popup_select_box">
      <div class="popup_select_head">
        <div class="popup_select_head_left">
          <text>{{ `${t('logout')} ${t('audit')}` }}</text>
        </div>
      </div>
      <div class="popup_select_content">
        <div class="popup_select_content_item">
          <div class="popup_select_content_item_title">
            <div>{{ t('binding_account') }}：{{ auditAccountData.emait }}</div>
            <div>{{ t('name') }}：{{ auditAccountData.userName }}</div>
            <div>{{ t('role_text') }}：{{ auditAccountData.roleName }}</div>
            <div
              >{{ t('cancelApplyTime') }}：{{ auditAccountData.createTime ? dayjs(auditAccountData.createTime).format('YYYY-MM-DD HH:mm') : '' }}</div
            >
            <div>{{ t('logoutAutoEndRemaining', { hours: formatMinutesToHourMinute(auditAccountData.approvalApprovalTime || 0, t) }) }}</div>
          </div>
          <div class="not_end_order" style="margin-top: 20px">
            <span style="margin-right: 10px">{{ t('noUnfinishedOrder') }}</span>
            <Icon color="#1990ff" name="success" v-if="notEndOrder" />
            <Icon color="#f12727" name="cross" v-else />
          </div>
          <div class="not_end_order" style="margin-top: 10px">
            <span style="margin-right: 10px">{{ t('noUnfinishedClue') }}</span>
            <Icon color="#1990ff" name="success" v-if="notEndClue" />
            <Icon color="#f12727" name="cross" v-else />
          </div>
          <div style="margin-top: 10px">
            <div>{{ t('auditResult') }}：</div>
            <div style="margin-top: 10px"
              ><radio-group v-model="auditParams.auditResult">
                <Radio name="1">{{ t('passText') }}</Radio>
                <Radio name="2">{{ t('noPassText') }}</Radio>
              </radio-group></div
            >
          </div>
          <div style="margin-top: 10px">
            <div>{{ t('auditResultRemark') }}：</div>
            <div>
              <textarea
                style="padding: 10px; width: 100%; margin-top: 10px; background: #f8f8f8; border-radius: 16px; height: 150px"
                v-model="auditParams.auditRemark"
              ></textarea>
            </div>
          </div>
        </div>

        <Button style="margin-top: 56px" type="primary" @click="submitAudit()">{{ t('submitAudit') }}</Button>
      </div>
    </div>
  </Popup>
</template>
<script lang="ts" setup name="AuditAccount">
  import { ref } from 'vue';
  import dayjs from 'dayjs';
  import { formatMinutesToHourMinute } from '/@/utils';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { showToast, Popup, Icon, Button, RadioGroup, Radio } from 'vant';
  import 'vant/lib/index.css';
  import Empty from '/@/views/h5/components/empty.vue';
  import { getOrderInfoByUserIdApi, getUncompletedClueByUserIdApi } from '/@/api/userCenter';
  import { auditCancellationApi, getZhuxiaoAuditInfoApi, getZhuxiaoRecordListApi } from '/@/api/h5Api/zhuxiao';

  const { t } = useI18n('common');

  // 审核提交
  const auditParams = ref({
    auditRecordId: '',
    auditResult: '',
    auditRemark: '',
  });

  // 离职注销审核-数据
  const invitationAccountList = ref([] as any[]);

  // 注销申请审核-弹窗是否显示
  const showApplyAuditAccount = ref(false);

  // 要审核的注销数据
  const auditAccountData = ref({} as any);
  // 无未完结工单
  const notEndOrder = ref(false);
  // 无未完结线索
  const notEndClue = ref(false);
  // 获取是否存在未完结工单、线索
  const getUncompletedOrderAndClue = async (userId: string) => {
    try {
      const orderRes = await getOrderInfoByUserIdApi({ userId });
      // @ts-ignore
      notEndOrder.value = orderRes.IsFinish;
    } catch (err) {}
    try {
      const uncompleteRes = await getUncompletedClueByUserIdApi({ userId });
      notEndClue.value = uncompleteRes.IsFinish;
    } catch (err) {}
  };

  // 初始化列表数据
  function initFn(params: any = {}) {
    getZhuxiaoRecordListApi(params).then((res) => {
      invitationAccountList.value = ((res || []) as any[]).map((mapItems) => {
        let statusName = '';
        let statusColor = '#1989fa';
        switch (mapItems.status) {
          case 2:
            statusName = t('approvalAuto');
            statusColor = '#1989fa';
            break;
          case 3:
            statusName = t('approvaled');
            statusColor = '#1989fa';
            break;
          case 1:
            statusName = t('approvalPending');
            statusColor = '#f12727';
            break;
        }
        return {
          ...mapItems,
          statusName,
          statusColor,
        };
      });
    });
  }

  // 审核离职注销
  const handleAuditAccount = async (item: any) => {
    if (item.status != 1) return;
    auditParams.value.auditRecordId = item.id;
    await initData(item.userId);
    await getUncompletedOrderAndClue(item.userId);
    showApplyAuditAccount.value = true;
  };
  // 初始化数据
  const initData = async (userId: string) => {
    try {
      const res = await getZhuxiaoAuditInfoApi({ userId });
      auditAccountData.value = res || null;
    } catch (err) {}
  };

  // loading
  const loading = ref(false);
  // 审核提交
  const submitAudit = async () => {
    if (!auditParams.value.auditResult) {
      showToast({
        message: t('pleaseSelectAuditResult'),
      });
      return;
    }
    if (!auditParams.value.auditRemark) {
      showToast({
        message: t('pleaseEnterAuditResultRemark'),
      });
      return;
    }
    if (loading.value) return;
    loading.value = true;
    try {
      await auditCancellationApi(auditParams.value);
      showToast({
        message: t('auditSubmissionSuccess'),
      });
      initFn();
      showApplyAuditAccount.value = false;
      loading.value = true;
    } catch (err) {
      loading.value = false;
    }
  };

  defineExpose({
    initFn,
  });
</script>
<script lang="ts">
  export default {
    name: 'AuditAccount',
  };
</script>
<style scoped lang="less">
  .invitation_link_list {
    padding: 0 10px;
  }
  .invitation_link_list_item {
    position: relative;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    padding: 10px 30px 10px 10px;
    margin-bottom: 10px;
    .item {
      display: flex;
      justify-content: space-between;
    }
    .item_content {
      color: #999;
      flex: 1;
      word-break: break-all;
    }
    .look_detail {
      position: absolute;
      right: 10px;
      top: 10px;
    }
  }
  ::v-deep(.van-popup__close-icon--top-right) {
    top: 10px;
  }
  ::v-deep(.van-button) {
    width: 100%;
  }
  .popup_select_box {
    width: 100%;
    height: 100%;
    .popup_select_head {
      width: 100%;
      height: 40px;
      background: #ffffff;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 15px 50px 10px 16px;
      box-sizing: border-box;
      color: #000000;
      font-weight: bolder;
      .popup_select_head_left {
        line-height: 20px;
      }
    }
    .popup_select_content {
      display: flex;
      flex-wrap: wrap;
      width: 100%;
      height: calc(100% - 40px);
      overflow: auto;
      padding: 16px;
      box-sizing: border-box;
      .popup_select_content_item {
        width: 100%;
        margin-right: 10px;
        margin-bottom: 10px;
      }
    }
  }
</style>
<style>
  .van-radio-group {
    display: flex;
  }
  .van-radio {
    margin-right: 20px;
  }
</style>
