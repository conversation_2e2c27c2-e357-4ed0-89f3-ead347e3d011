<template>
  <a-card>
    <BasicForm @register="registerForm" @submit="handleSubmit" @cancel="handleCancel" />
  </a-card>
</template>

<script setup lang="ts">
  import { useForm, BasicForm, FormSchema } from '/@/components/Form';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { router } from '/@/router';
  import { useRoutePageType } from '/@/hooks/route';
  import { PageTypeEnum } from '/@/enums/common';
  import { onMounted, ref } from 'vue';
  import {
    featchAddCarModel,
    featchUpdateCarModel,
    featchCarModelDetail,
    CarModelType,
    getCarBrandModelListAll,
  } from '/@/api/basicData/carModelCarSeries';
  import { awaitTo } from '@ruqi/utils-admin';
  import { CarTypeOptions, PowerTypeOptions } from '/@/enums/basicData';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useSessionStorageState } from 'vue-hooks-plus';
  import { useTabs } from '/@/hooks/web/useTabs';

  const { t } = useI18n('common');
  interface SeriesOption {
    label: string;
    value: string;
    outCode: string;
  }
  const pageType = useRoutePageType();
  const seriesList = ref<SeriesOption[]>([]);
  const disabled = pageType === PageTypeEnum.VIEW;

  const formSchemas: FormSchema[] = [
    {
      label: t('modelName'),
      field: 'modelName',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      required: true,
      componentProps: {
        placeholder: t('pleaseEnterContent'),
        disabled,
      },
    },
    {
      label: t('modelCode'),
      field: 'modelCode',
      labelWidth: '100%',
      component: 'Input',
      colProps: { span: 8 },
      required: true,
      componentProps: {
        placeholder: t('pleaseEnterContent'),
        disabled: pageType !== PageTypeEnum.ADD,
      },
    },
    {
      label: t('powerType'),
      field: 'productMark',
      labelWidth: '100%',
      component: 'Select',
      colProps: { span: 8 },
      componentProps: {
        disabled,
        options: PowerTypeOptions,
      },
    },
    {
      label: t('vehicleType'),
      field: 'carTypeLv',
      labelWidth: '100%',
      component: 'Select',
      colProps: { span: 8 },
      componentProps: {
        disabled,
        options: CarTypeOptions,
      },
    },
    {
      label: t('brandOwnershipName'),
      field: 'brandCode',
      labelWidth: '100%',
      colProps: { span: 8 },
      component: 'ApiSelect',
      required: true,
      componentProps: {
        disabled,
        placeholder: t('chooseText'),
        api: getCarBrandModelListAll,
        labelField: 'brandName',
        valueField: 'brandCode',
        showSearch: true,
        params: {
          level: 2,
        },
        onChange: async (value: string, option: any) => {
          if (!option) return;
          const carSeriesList: any = option.carSeries;
          seriesList.value = carSeriesList.map((item) => ({
            label: item.seriesName,
            value: item.seriesCode,
            outCode: item.outCode || '',
          }));
          setFieldsValue({
            seriesCode: null,
            carSeriesOutCode: null,
            carBrandOutCode: option?.outCode || '',
            brandCodeShow: value,
          });
        },
        filterOption: (input: string, option: any) => {
          return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
        },
      },
    },
    {
      label: t('brandOwnershipCode'),
      field: 'brandCodeShow',
      component: 'Input',
      colProps: { span: 8 },
      required: true,
      labelWidth: '100%',
      componentProps: {
        placeholder: t('pleaseEnterContent'),
        disabled: true,
      },
    },
    {
      label: '',
      field: 'carBrandOutCode',
      labelWidth: '100%',
      colProps: { span: 8 },
      component: 'Input',
      show: false,
    },
    {
      label: '',
      field: 'carSeriesOutCode',
      labelWidth: '100%',
      colProps: { span: 8 },
      component: 'Input',
      show: false,
    },
    {
      label: t('carSeriesOwnershipName'),
      field: 'seriesCode',
      labelWidth: '100%',
      component: 'Select',
      required: true,
      colProps: { span: 8 },
      componentProps: {
        disabled,
        options: seriesList,
        onChange: (value: string, option: any) => {
          if (!option) return;
          setFieldsValue({
            seriesCodeShow: value,
            carSeriesOutCode: option?.outCode || '',
          });
        },
      },
    },
    {
      label: t('carSeriesOwnershipCode'),
      field: 'seriesCodeShow',
      component: 'Input',
      colProps: { span: 8 },
      labelWidth: '100%',
      required: true,
      componentProps: {
        placeholder: t('pleaseEnterContent'),
        disabled: true,
      },
    },
  ];

  /** 提交按钮配置 */
  const submitButtonOptions = ref({ text: t('saveText'), preIcon: '', loading: false });

  const [VehicleModelItem] = useSessionStorageState('VehicleModelItem', {
    defaultValue: {} as CarModelType,
  });

  const id = VehicleModelItem.value?.id ?? -1;

  const [registerForm, { setFieldsValue }] = useForm({
    schemas: formSchemas,
    autoSubmitOnEnter: true,
    showResetButton: false,
    showCancelButton: true,
    cancelButtonOptions: { text: t(`${pageType === PageTypeEnum.VIEW ? 'back' : 'cancelText'}`), preIcon: '' },
    showSubmitButton: [PageTypeEnum.EDIT, PageTypeEnum.ADD].some((v) => v === pageType),
    submitButtonOptions,
    buttonLayout: 'left',
    labelAlign: 'left',
    layout: 'vertical',
  });

  const { createMessage } = useMessage();
  const { success } = createMessage;

  const apiMap = {
    [PageTypeEnum.ADD]: featchAddCarModel,
    [PageTypeEnum.EDIT]: featchUpdateCarModel,
  };

  const { close: closeTab } = useTabs();

  const getDetail = async () => {
    const [, content] = await awaitTo(featchCarModelDetail({ id }));
    if (!content) return;
    const data = { ...content, seriesCodeShow: content.seriesCode, brandCodeShow: content.brandCode };
    if (content?.brandCode) {
      const [, brandList] = await awaitTo(getCarBrandModelListAll({ level: 2 }));
      if (brandList) {
        const brand = brandList.find((item) => item.brandCode === content.brandCode);
        if (brand) {
          seriesList.value = brand.carSeries.map((item) => ({
            label: item.seriesName,
            value: item.seriesCode,
            outCode: item.outCode || '',
          }));
        }
      }
    }
    setFieldsValue(data);
  };

  const handleSubmit = async (values: Record<string, any>) => {
    if (Reflect.ownKeys(values).length === 0) return;
    submitButtonOptions.value.loading = true;
    const api = apiMap[pageType];
    const params = values;
    if (pageType === PageTypeEnum.EDIT) {
      params.id = id;
    }
    delete params.seriesCodeShow;
    delete params.brandCodeShow;
    const [err] = await awaitTo(api(params));
    if (!err) {
      success(t('operationSuccess'));
      handleCancel();
    }
    submitButtonOptions.value.loading = false;
  };

  const handleCancel = () => {
    closeTab();
    router.push('/basic-data/car-model-car-series/vehicle-model-management');
  };

  onMounted(() => {
    if (pageType !== PageTypeEnum.ADD) {
      getDetail();
    }
  });
</script>

<style lang="less" scoped>
  ::v-deep(.ant-card-head) {
    border-bottom: none;
  }
</style>
