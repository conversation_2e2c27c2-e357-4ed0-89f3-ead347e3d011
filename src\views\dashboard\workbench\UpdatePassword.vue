<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    :closable="false"
    :maskClosable="false"
    :title="title"
    :cancelText="t('common.logout_button')"
    @ok="handleSubmit"
    @cancel="handleCancel"
    width="600px"
    :maxHeight="466"
  >
    <!-- 首次登录修改密码提示 -->
    <div v-if="isFirstEditPassword" style="padding-left: 34px; font-size: 20px; line-height: 40px">{{ t('common.edit_password_tip1') }}</div>
    <!-- 90天后登录修改密码提示 -->
    <div v-else style="padding-left: 34px; font-size: 20px; line-height: 40px">{{ t('common.edit_password_tip') }}</div>
    <BasicForm @register="registerForm" style="padding-left: 36px">
      <template #password="{ model, field }">
        <a-input type="password" v-model:value="model[field]" />
        <div style="margin-top: 20px; color: #86909c; font-size: 16px; line-height: 30px">
          <div>{{ t('common.password_rule1') }}</div>
          <div>{{ t('common.password_rule2') }}</div>
          <div>{{ t('common.password_rule3') }}</div>
        </div>
      </template>
      <template #newPassword="{ model, field }">
        <a-input type="password" v-model:value="model[field]" />
      </template>
      <template #code="{ model, field }">
        <div class="flex">
          <a-input
            style="width: 186px"
            class="fix-auto-fill"
            type="text"
            :placeholder="t('common.inputCodePlaceholder')"
            v-model:value="model[field]"
          />
          <div class="aui-code" style="margin-top: 5px">
            <span
              v-if="isShowGetCode === true"
              style="margin-left: 5px; cursor: pointer; font-size: 14px; color: #1990ff"
              @click.stop="handleChangeCheckCode"
              >{{ t('common.obtain_verification_code_text') }}</span
            >
            <StatisticCountdown
              v-if="isShowGetCode === false"
              :valueStyle="{ color: '#1990FF', fontSize: '14px', marginLeft: '5px' }"
              :value="time"
              :format="`ss${t('common.retrieveSsSecondsLater')}`"
              @finish="isShowGetCode = true"
            />
          </div>
        </div>
      </template>
    </BasicForm>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { ref, computed } from 'vue';
  import { StatisticCountdown } from 'ant-design-vue';
  import { rules } from '/@/utils/helper/validator';
  import { passwordRule } from '/@/utils/validator';
  import { defHttp } from '/@/utils/http/axios';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import BasicForm from '/@/components/Form/src/BasicForm.vue';
  import { useForm } from '/@/components/Form/src/hooks/useForm';
  import { useUserStore } from '/@/store/modules/user';
  import { resetPwdSendMsgApi } from '/@/api/common/api';
  import { useI18n } from '/@/hooks/web/useI18n';
  import rsaEncryptor from '/@/utils/rsaEncrypt';
  const { t } = useI18n();

  const { createMessage } = useMessage();
  const $message = useMessage();
  const userStore = useUserStore();
  // update-begin--author:liaozhiyang---date:********---for：【QQYUN-7970】国际化
  const title = ref(t('common.changePassword_changePassword'));

  // 展示的密码提示-是否首次修改密码
  const isFirstEditPassword = computed(() => {
    // @ts-ignore
    return !userStore.getUserInfo.pwdChangeRequired;
  });

  // 是否展示重新获取验证码
  const isShowGetCode = ref(true);
  const loading = ref(false);

  const time = ref(Date.now() + 1000 * 60);

  /**
   * 获取验证码
   */
  async function handleChangeCheckCode() {
    const params = getFieldsValue();
    if (!params.account) {
      return $message.createMessage.warning(`${t('common.chooseText')}${t('common.yanzheng_fangshi')}`);
    }
    if (loading.value) {
      return;
    }
    loading.value = true;
    try {
      await resetPwdSendMsgApi({
        account: params.account,
      });
      createMessage.success(t('common.verification_code_sent_successfully'));
      isShowGetCode.value = false;
      time.value = Date.now() + 1000 * 60;
    } catch (err) {}
    setTimeout(() => {
      loading.value = false;
    }, 2000);
  }

  //表单配置
  const [registerForm, { resetFields, validate, clearValidate, updateSchema, getFieldsValue }] = useForm({
    schemas: [
      {
        label: t('common.changePassword_oldPassword'),
        field: 'oldpassword',
        component: 'InputPassword',
        required: true,
        labelWidth: '100%',
      },
      {
        label: t('common.changePassword_newPassword'),
        field: 'password',
        slot: 'password',
        component: 'Input',
        componentProps: {
          placeholder: t('common.changePassword_pleaseEnterNewPassword'),
        },
        rules: [
          {
            required: true,
            message: t('common.changePassword_pleaseEnterNewPassword'),
          },
        ],
        labelWidth: '100%',
      },
      {
        label: t('common.changePassword_confirmNewPassword'),
        field: 'confirmpassword',
        component: 'Input',
        slot: 'newPassword',
        dynamicRules: ({ values }) => rules.confirmPassword(values, true),
        labelWidth: '100%',
      },
      {
        label: t('common.yanzheng_fangshi'),
        field: 'account',
        component: 'Select',
        required: true,
        labelWidth: '100%',
      },
      {
        label: t('common.inputCode'),
        field: 'code',
        component: 'Input',
        required: true,
        slot: 'code',
        labelWidth: '100%',
      },
    ],
    showActionButtonGroup: false,
    buttonLayout: 'right',
    layout: 'vertical',
  });
  // update-end--author:liaozhiyang---date:********---for：【QQYUN-7970】国际化
  //表单赋值
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (_data) => {
    isShowGetCode.value = true;
    loading.value = false;
    time.value = Date.now() + 1000 * 60;
  });

  // 是否包含用户名
  function containsString(str, target) {
    if (!target) {
      return false;
    }
    const regex = new RegExp(target, 'i'); // 'i' 表示不区分大小写
    return !!regex.test(str);
  }

  // 表单提交事件
  async function handleSubmit() {
    try {
      const values = await validate();
      setModalProps({ confirmLoading: true });

      if (!passwordRule.test(values.confirmpassword)) {
        return $message.createMessage.warning(`${t('common.password_rule1')};${t('common.password_rule2')}`);
      }
      if (containsString(values.confirmpassword, userStore.getUserInfo.englishName)) {
        return $message.createMessage.warning(t('common.password_rule3'));
      }
      //提交表单
      let params = {
        oldPassword: rsaEncryptor.encrypt(values.oldpassword),
        code: values.code,
        account: values.account,
        newPassword: rsaEncryptor.encrypt(values.confirmpassword),
      };
      defHttp.post({ url: '/sys/user/resetPwd', params }, { isTransformResponse: false }).then((res) => {
        if (res.code === 0) {
          $message.createMessage.success(res.msg);
          userStore.getUserInfoAction();
          //关闭弹窗
          closeModal();
        } else {
          $message.createMessage.warning(res.msg);
        }
      });
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }

  // 表单取消事件
  async function handleCancel() {
    setModalProps({ visible: true });
    handleLoginOut();
  }

  //  login out
  function handleLoginOut() {
    userStore.confirmLoginOut();
  }

  async function show(_name?: string) {
    isShowGetCode.value = true;
    loading.value = false;
    time.value = Date.now() + 1000 * 60;
    setModalProps({ visible: true });
    await resetFields();
    await clearValidate();
    const { mobile, email } = userStore.getUserInfo;
    updateSchema({
      field: 'account',
      componentProps: {
        options: [
          {
            label: t('common.mobile'),
            value: mobile,
            disabled: !mobile,
          },
          {
            label: t('common.email_text'),
            value: email,
            disabled: !email,
          },
        ],
      },
    });
  }

  defineExpose({
    title,
    show,
  });
</script>
