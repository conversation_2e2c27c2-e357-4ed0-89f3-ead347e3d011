<template>
  <div :class="['p-15px']">
    <div :class="['p-15px', prefixCls]" v-loading="submitButtonOptions.loading">
      <BasicForm @register="registerForm" @submit="handleSubmit" @cancel="handleCancel">
        <!-- 分配的菜单 -->
        <template #treeRoleSlots>
          <div class="mt-20px">
            <BasicTree
              checkable
              :treeData="unifiedTreeData"
              :checkedKeys="checkedKeys"
              :checkStrictly="false"
              :expandedKeys="expandedKeys"
              :clickRowToExpand="false"
              :title="t('common.assign_permissions_text')"
              :fieldNames="{ title: 'text', key: 'id', children: 'children' }"
              @check="onCheck"
            >
              <template #title="{ text }">
                {{ text }}
              </template>
            </BasicTree>
          </div>
        </template>
      </BasicForm>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted } from 'vue';
  import { useRouter, useRoute } from 'vue-router';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { BasicTree } from '/@/components/Tree';
  import { useTabs } from '/@/hooks/web/useTabs';
  import { useRoutePageType } from '/@/hooks/route';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { PageTypeEnum } from '/@/enums/common';
  import { useUserStore } from '/@/store/modules/user';
  import { assignPermissionsFormSchema } from '../role.data';
  import { treeByRoleApi, roleAssignPermissionsApi } from '../role.api';
  import { getBackMenuAndPerms } from '/@/api/sys/menu';
  import { useI18n } from '/@/hooks/web/useI18n';

  const { t } = useI18n();
  let router = useRouter();
  const { query: urlParams } = useRoute();
  const { userInfo } = useUserStore();

  const { close: closeTab } = useTabs();

  // 页面操作类型
  const pageType = useRoutePageType();

  const prefixCls = ref('role-and-menu-handle');

  // 按钮提交配置
  const submitButtonOptions = ref({ text: t('saveText'), preIcon: '', loading: false });

  const { createMessage } = useMessage();

  const { success } = createMessage;

  // 统一的权限树数据
  const unifiedTreeData = ref([] as any);
  const checkedKeys = ref<string[]>([]);
  const halfCheckedKeys = ref<string[]>([]);
  const expandedKeys = ref<string[]>([]);

  // 菜单分类映射，用于提交时按分类组织数据
  const categoryMapping = ref<Record<string, string>>({});

  // 表单配置
  const [registerForm, { setFieldsValue, updateSchema }] = useForm({
    schemas: assignPermissionsFormSchema,
    baseColProps: { span: 12 },
    labelWidth: 220,
    autoSubmitOnEnter: true,
    showActionButtonGroup: unifiedTreeData.value.length <= 0,
    showResetButton: false,
    showCancelButton: true,
    cancelButtonOptions: { text: t(`${pageType === PageTypeEnum.ViewAssignPermissions ? 'common.back' : 'common.cancelText'}`), preIcon: '' },
    showSubmitButton: [PageTypeEnum.AssignPermissions].some((v) => v === pageType),
    submitButtonOptions: { text: t('common.saveText'), preIcon: '' },
    buttonLayout: 'left',
    labelAlign: 'left',
    layout: 'vertical',
  });

  // 表单提交
  const handleSubmit = async () => {
    submitButtonOptions.value.loading = true;
    const { roleId } = urlParams as any;
    try {
      let roleAssignPermissionsList: any = [];

      // 合并完全选中和半选中的节点
      const allSelectedKeys = [...new Set([...checkedKeys.value, ...halfCheckedKeys.value])];

      // 按分类组织权限数据
      const categoryPermissions: Record<string, string[]> = {};

      allSelectedKeys.forEach((nodeId) => {
        const categoryId = categoryMapping.value[nodeId];
        if (categoryId) {
          if (!categoryPermissions[categoryId]) {
            categoryPermissions[categoryId] = [];
          }
          categoryPermissions[categoryId].push(nodeId);
        }
      });

      // 为每个分类构建提交数据
      Object.keys(categoryPermissions).forEach((categoryId) => {
        const permissions = categoryPermissions[categoryId];

        // 添加权限节点
        const permissionData = permissions.map((menuId) => ({ menuId }));
        roleAssignPermissionsList.push(...permissionData);

        // 添加分类节点本身
        roleAssignPermissionsList.push({ menuId: categoryId });
      });

      await roleAssignPermissionsApi({
        roleId,
        menuAssignPermissionsList: roleAssignPermissionsList,
      });
      success(t('common.operationSuccess'));
      handleCancel();
    } finally {
      submitButtonOptions.value.loading = false;
    }
  };

  // 返回上一级
  const handleCancel = () => {
    closeTab();
    router.push('/system/role');
  };

  // 递归设置菜单树节点为禁用状态
  function getTreeDisableds(treeData: any) {
    treeData.forEach((item: any) => {
      item.disabled = true;
      if (item.children) {
        getTreeDisableds(item.children);
      }
    });
    return treeData;
  }

  /**
   * 统一Tree的选中事件处理 - 使用防抖优化性能
   */
  let debounceTimer: NodeJS.Timeout | null = null;
  function onCheck(checkedKeysValue, e) {
    // 清除之前的定时器
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }

    // 使用防抖，减少频繁的状态更新
    debounceTimer = setTimeout(() => {
      checkedKeys.value = checkedKeysValue;
      halfCheckedKeys.value = e.halfCheckedKeys || [];
    }, 50); // 50ms防抖延迟
  }

  // 从权限树中提取所有叶子节点ID（实际被授权的最终节点）
  function getLeafNodeIds(treeData: any, leafIds: string[] = []) {
    treeData.forEach((item: any) => {
      if (!item.children || item.children.length === 0) {
        // 叶子节点
        leafIds.push(item.id);
      } else {
        // 继续递归查找叶子节点
        getLeafNodeIds(item.children, leafIds);
      }
    });
    return leafIds;
  }

  // 获取当前登录用户的菜单并构建统一的权限树
  async function getBackMenuAndPermsFn(roleId: string) {
    try {
      const havePermissionMenuRes = await treeByRoleApi([roleId]);
      const permissionList = havePermissionMenuRes?.children || [];
      const systemPermission: any = await getBackMenuAndPerms({
        userId: userInfo?.userId ? String(userInfo?.userId) : '',
      });

      // 构建统一的权限树数据
      const allSelectedIds: string[] = [];
      const categoryExpandedIds: string[] = []; // 只展开菜单分类，不展开所有子节点

      // 将所有菜单分类作为根节点，构建统一的树结构
      unifiedTreeData.value =
        systemPermission.allMenu?.children?.map((mapItems) => {
          const findTypeParams = permissionList.find((findItems) => findItems.id === mapItems.id);

          // 获取当前分类下已选中的权限节点
          const selectIds = !!findTypeParams ? getLeafNodeIds(findTypeParams?.children || []) : [];
          allSelectedIds.push(...selectIds);
          categoryExpandedIds.push(mapItems.id); // 只展开分类节点本身

          // 为每个权限节点建立与分类的映射关系
          const buildCategoryMapping = (children: any[], categoryId: string) => {
            children.forEach((child) => {
              categoryMapping.value[child.id] = categoryId;
              if (child.children) {
                buildCategoryMapping(child.children, categoryId);
              }
            });
          };
          buildCategoryMapping(mapItems?.children || [], mapItems.id);

          let children = mapItems?.children || [];
          if ([PageTypeEnum.ViewAssignPermissions].some((v) => v === pageType)) {
            children = getTreeDisableds(children);
          }

          return {
            id: mapItems.id,
            text: mapItems.text,
            children: children,
          };
        }) || [];

      // 设置统一Tree的初始状态
      checkedKeys.value = allSelectedIds;
      halfCheckedKeys.value = [];
      expandedKeys.value = categoryExpandedIds; // 只展开分类节点，提升性能
    } catch (err) {
      console.error('获取菜单权限失败:', err);
    }
  }
  onMounted(async () => {
    submitButtonOptions.value.loading = true;
    await updateSchema(
      assignPermissionsFormSchema.map((mapItems) => {
        return {
          ...mapItems,
          dynamicDisabled: true,
        };
      })
    );
    const { roleId, roleName, remark, status, countryCode, regionCode, roleSign } = urlParams as any;
    await setFieldsValue({
      roleId: roleId,
      roleName: roleName,
      remark: remark,
      roleSign,
      region_country_code: regionCode && countryCode ? `${regionCode}_${countryCode}` : undefined,
      status,
    });

    if (!roleId) {
      submitButtonOptions.value.loading = false;
      return;
    }
    await getBackMenuAndPermsFn(roleId || '');
    submitButtonOptions.value.loading = false;
  });
</script>
<style lang="less" scoped>
  .role-and-menu-handle {
    background: @component-background;
    height: calc(100vh - 140px);
    overflow: auto;
  }

  ::v-deep(.btnArea) {
    position: fixed;
    right: 20px;
    bottom: 20px;
    width: inherit;
    width: initial !important;
  }
</style>
