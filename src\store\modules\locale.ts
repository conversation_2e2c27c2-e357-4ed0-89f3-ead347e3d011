import type { LocaleSetting, LocaleType } from '/#/config';

import { defineStore } from 'pinia';
import { store } from '/@/store';
import { LOCALE_KEY } from '/@/enums/cacheEnum';
import { createLocalStorage } from '/@/utils/cache';
import { LOCALE, localeSetting } from '/@/settings/localeSetting';

/** RTL 布局下 箭头 icon 右边距 css 属性 */
const RTL_MENU_ICON_ITEM_MR_CSS_PROPERTY = '--rtl-menu-item-margin-right';

const ls = createLocalStorage();

const lsLocaleSetting = (ls.get(LOCALE_KEY) || localeSetting) as LocaleSetting;

interface LocaleState {
  localInfo: LocaleSetting;
  pathTitleMap: object;
  // myapps主题色（低代码应用列表首页）
  appIndexTheme: string;
  // myapps - 跳转前路由地址
  appMainPth: string;
}

export const useLocaleStore = defineStore({
  id: 'app-locale',
  state: (): LocaleState => ({
    localInfo: lsLocaleSetting,
    pathTitleMap: {},
    appIndexTheme: '',
    appMainPth: '',
  }),
  getters: {
    getShowPicker(): boolean {
      return !!this.localInfo?.showPicker;
    },
    getLocale(): LocaleType {
      return this.localInfo?.locale ?? 'en-US';
    },
    //update-begin-author:taoyan date:2022-6-1 for: VUEN-1144 online 配置成菜单后，打开菜单，显示名称未展示为菜单名称
    getPathTitle: (state) => {
      return (path) => state.pathTitleMap[path];
    },
    //update-end-author:taoyan date:2022-6-1 for: VUEN-1144 online 配置成菜单后，打开菜单，显示名称未展示为菜单名称
    getAppIndexTheme(): string {
      return this.appIndexTheme;
    },
    getAppMainPth(): string {
      return this.appMainPth;
    },
  },
  actions: {
    /**
     * Set up multilingual information and cache
     * @param info multilingual info
     */
    setLocaleInfo(info: Partial<LocaleSetting>) {
      this.localInfo = { ...this.localInfo, ...info };
      const locale = info.locale;
      // 对阿拉伯语特殊处理 注入html文档属性 dir="rtl"
      const timer = setTimeout(() => {
        if (locale === LOCALE.AR) {
          document.documentElement.setAttribute('dir', 'rtl');
          document.documentElement.style.setProperty(RTL_MENU_ICON_ITEM_MR_CSS_PROPERTY, '15px');
        } else {
          document.documentElement.removeAttribute('dir');
          document.documentElement.style.removeProperty(RTL_MENU_ICON_ITEM_MR_CSS_PROPERTY);
        }
        clearTimeout(timer);
      }, 200)
      ls.set(LOCALE_KEY, this.localInfo);
    },
    /**
     * Initialize multilingual information and load the existing configuration from the local cache
     */
    initLocale() {
      this.setLocaleInfo({
        ...localeSetting,
        ...this.localInfo,
      });
    },
    //update-begin-author:taoyan date:2022-6-1 for: VUEN-1144 online 配置成菜单后，打开菜单，显示名称未展示为菜单名称
    setPathTitle(path, title) {
      this.pathTitleMap[path] = title;
    },
    //update-end-author:taoyan date:2022-6-1 for: VUEN-1144 online 配置成菜单后，打开菜单，显示名称未展示为菜单名称
    setAppIndexTheme(theme) {
      this.appIndexTheme = theme;
    },
    setAppMainPth(path) {
      this.appMainPth = path;
    },
  },
});

// Need to be used outside the setup
export function useLocaleStoreWithOut() {
  return useLocaleStore(store);
}
