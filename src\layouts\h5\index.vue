<template>
  <div :class="isMicroAppRoute ? 'h5-layout-micro-app' : 'h5-layout'">
    <!-- 页面内容区域,需要特殊处理，如果当前路由是子应用，则不显示内容区域，使用子应用的容器 -->
    <div class="h5-layout-content" v-if="!isMicroAppRoute">
      <router-view />
      <!-- 底部标签栏 -->
      <Tabbar v-if="isShowTabbar" />
    </div>

    <!-- H5子应用容器 - 固定在布局中，不随路由变化 -->
    <!-- <div id="h5-content" class="h5-app-container"></div> -->
    <div id="h5-content" class="h5-app-container" :style="{ display: isMicroAppRoute ? 'block' : 'none' }"></div>
  </div>
</template>

<script lang="ts" setup name="H5Layout">
  import { ref, onMounted, nextTick, computed } from 'vue';
  import { useRoute } from 'vue-router';
  import { useGlobSetting } from '/@/hooks/setting';
  import { isMobile } from '/@/utils';
  import registerApps from '/@/qiankun';
  import Tabbar from '/@/views/h5/components/tabbar.vue';
  import 'vant/lib/index.css'; // 这里统一引入vant的样式，避免在子路由中重复引入
  // import NavBar from '/@/components/NavBar.vue';

  // 声明全局属性
  declare global {
    interface Window {
      h5QiankunStarted?: boolean;
    }
  }

  const globSetting = useGlobSetting();
  const openQianKun = globSetting.openQianKun;
  const active = ref(0);
  const route = useRoute();

  // 是否显示tabbar
  const isShowTabbar = computed(() => {
    // 处理路由参数的大小写，可以包含
    const lowerCasePath = route.path.toLowerCase();
    return ['/mobile/h5PersonalCenter', '/mobile/h5Workbenches', '/mobile/h5ToDoCenter', '/mobile/h5Home'].some((item) => {
      return item.toLowerCase() === lowerCasePath;
    });
  });

  // 判断当前路由是否为子应用路由
  const isMicroAppRoute = computed(() => {
    return route.path.startsWith('/gac_iop_childapp');
  });

  onMounted(() => {
    // 根据当前路由路径设置活动标签
    updateActiveTab();
    // 确保DOM已渲染完成后再初始化微应用
    nextTick(() => {
      // 检查容器是否存在
      if (!document.getElementById('h5-content')) {
        console.error('H5子应用容器不存在！');
        return;
      }

      // 在H5环境下初始化微应用，兼容处理下 H5 代办页
      if (openQianKun === 'true' && isMobile() === true && route.path.includes('/mobile/h5ToDoCenter')) {
        if (!window.h5QiankunStarted) {
          window.h5QiankunStarted = true;
          console.log('初始化H5子应用...');
          // 延迟注册，确保容器完全准备好
          setTimeout(() => {
            registerApps(true); // 传入true表示H5环境
          }, 100);
        }
      }
    });
  });

  // 更新活动标签
  const updateActiveTab = () => {
    switch (route.path) {
      case '/mobile/h5Home':
        active.value = 0;
        break;
      case '/mobile/h5ToDoCenter':
        active.value = 1;
        break;
      case '/mobile/h5Workbenches':
        active.value = 2;
        break;
      case '/mobile/h5PersonalCenter':
        active.value = 3;
        break;
    }
  };
</script>

<style scoped lang="less">
  .h5-layout-micro-app {
    width: 100%;
    min-height: 100vh;
    background: #ffffff;
    padding: 0;
  }
  .h5-layout {
    width: 100%;
    min-height: 100vh;
    box-sizing: border-box;
    background: #ffffff;
    display: flex;
    flex-direction: column;

    &-content {
      flex: 1;
      overflow-y: auto;
      padding-bottom: 50px; // 预留tabbar高度
    }
  }

  .h5-app-container {
    width: 100%;
    flex: 1;
    overflow: hidden;
  }
</style>
