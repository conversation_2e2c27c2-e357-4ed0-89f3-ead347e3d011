<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :width="1100" :maxHeight="580" :title="pageTitle" @ok="handleSubmit" destroyOnClose>
    <BasicTable @register="registerTable" style="width: 96%">
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>
    <BasicTable @register="haveRegisterTable" style="width: 96%">
      <template #action="{ record }">
        <TableAction :actions="getHaveTableAction(record)" />
      </template>
    </BasicTable>
  </BasicModal>
</template>
<script lang="ts">
  export default defineComponent({
    name: 'AssigningRolesModal',
  });
</script>
<script lang="ts" setup>
  import { defineComponent, ref, reactive } from 'vue';
  import { message } from 'ant-design-vue';
  import { BasicTable, TableAction } from '/@/components/Table';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { list } from '/@/views/system/role/role.api';
  import { queryUserInfoApi, bindRolesApi } from './user.api';
  import { RoleTypeOptions } from '@/enums/roleEnum';
  import { getLabelFromDict } from '@ruqi/utils-admin';
  import { featchStoreRoleAuthorize } from '@/api/sys/store';
  import { useUserStore } from '/@/store/modules/user';

  const { t } = useI18n();
  const { userInfo } = useUserStore();
  const props = defineProps({
    pageType: {
      type: String,
      default: '',
    },
  });
  // 声明Emits
  const emit = defineEmits(['success', 'register']);

  const userId = ref('');
  const haeRoles = ref([] as any);
  // 传入页面的参数
  let pageParams = reactive({} as any);
  const pageTitle = ref(props.pageType === 'store' ? t('common.authRole') : t('common.assign_roles_text'));
  const authRoleText = ref(props.pageType === 'store' ? t('common.authorizedRole') : t('common.account_associated_roles_text'));

  // 列表页面公共参数、方法-未分配的角色
  const { tableContext } = useListPage({
    designScope: 'not-select-role-template',
    tableProps: {
      title: t('common.search_result_text'),
      showTableSetting: false,
      rowKey: 'roleId',
      immediate: false,
      columns: [
        {
          title: t('common.role_id_text'),
          dataIndex: 'roleId',
          width: 120,
        },
        {
          title: t('common.role_name_text'),
          dataIndex: 'roleName',
          width: 120,
        },
        {
          title: t('common.roleType'),
          dataIndex: 'roleType',
          width: 120,
          customRender: ({ text }) => getLabelFromDict(text, RoleTypeOptions),
        },
      ],
      pagination: false,
      formConfig: {
        labelWidth: 65,
        rowProps: { gutter: 24 },
        schemas: [
          {
            field: 'roleId',
            label: t('common.role_id_text'),
            component: 'Input',
            colProps: { span: 8 },
            dynamicRules: () => {
              return [{ pattern: /^[1-9]\d*$/, message: t('common.pleaseEnterPositiveInteger'), trigger: 'blur' }];
            },
          },
          {
            field: 'roleName',
            label: t('common.role_name_text'),
            component: 'Input',
            colProps: { span: 8 },
          },
          {
            label: t('common.roleType'),
            field: 'roleType',
            component: 'Select',
            colProps: { span: 8 },
            componentProps: {
              options: RoleTypeOptions,
            },
          },
        ],
        submitFunc: async () => {
          initRole();
        },
      },
      actionColumn: {
        width: 96,
      },
    },
  });
  const [
    registerTable,
    {
      getForm: setNotHaveGetForm,
      setTableData: setNotHaveTableData,
      deleteTableDataRecord: deleteNotHaveTableDataRecord,
      insertTableDataRecord: insertNotHaveTableDataRecord,
    },
  ] = tableContext;
  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      // 綁定角色
      {
        label: t('common.bind_role_text'),
        onClick: handleBindRole.bind(null, record),
      },
    ];
  }
  // 绑定角色
  const handleBindRole = (record) => {
    // 删除未绑定角色表格
    deleteNotHaveTableDataRecord(record.roleId);
    // 插入到已绑定角色表格
    insertHaveTableDataRecord(record, -1);
  };

  // 列表页面公共参数、方法-已分配的角色
  const { tableContext: haveTableContext } = useListPage({
    designScope: 'not-select-role-template',
    tableProps: {
      title: authRoleText,
      showTableSetting: false,
      immediate: false,
      pagination: false,
      rowKey: 'roleId',
      columns: [
        {
          title: t('common.role_id_text'),
          dataIndex: 'roleId',
          width: 120,
        },
        {
          title: t('common.role_name_text'),
          dataIndex: 'roleName',
          width: 120,
        },
        {
          title: t('common.roleType'),
          dataIndex: 'roleType',
          width: 120,
          customRender: ({ text }) => getLabelFromDict(text, RoleTypeOptions),
        },
      ],
      useSearchForm: false,
      actionColumn: {
        width: 96,
      },
    },
  });
  const [
    haveRegisterTable,
    {
      getDataSource: getHaveDataSource,
      setTableData: setHaveTableData,
      deleteTableDataRecord: deleteHaveTableDataRecord,
      insertTableDataRecord: insertHaveTableDataRecord,
    },
  ] = haveTableContext;

  /**
   * 操作栏
   */
  function getHaveTableAction(record) {
    return [
      // 解除绑定
      {
        label: t('common.unbind_role_text'),
        onClick: handleJiechuBindRole.bind(null, record),
      },
    ];
  }
  // 解除绑定角色
  const handleJiechuBindRole = (record) => {
    // 删除已绑定角色表格
    deleteHaveTableDataRecord(record.roleId);
    // 插入到未绑定角色表格
    insertNotHaveTableDataRecord(record, -1);
  };

  async function initRole(initValue = false) {
    const params = (await setNotHaveGetForm().validate()) || {};
    try {
      const notHaveRoles = await list({
        pageIndex: 1,
        pageSize: 100000,
        ...params,
        status: 1, // 获取启用的角色
        accountCategory: pageParams?.authorizeLevel || userInfo?.accountCategory || 1,
      });
      let haveRoles: any = [];
      if (initValue) {
        if (userId.value) {
          const res = await queryUserInfoApi({ userId: userId.value });
          const { user } = res || {};
          const userInfo = user || {};
          haveRoles = userInfo?.roleIds || [];
        } else {
          haveRoles = pageParams?.roleIds || [];
        }
        // 设置已分配的表格数据
        setHaveTableData(
          notHaveRoles.filter((item) => {
            return !!haveRoles.includes(item.roleId);
          })
        );
        haeRoles.value = haveRoles;
      } else {
        haveRoles = getHaveDataSource()?.map((mapItems) => mapItems.roleId) || [];
      }

      // 对未分配的角色进行已分配角色排除
      setNotHaveTableData(
        notHaveRoles.filter((item) => {
          return !haveRoles.includes(item.roleId);
        })
      );
    } catch (err) {}
  }

  // 表单赋值
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    setModalProps({ confirmLoading: true, loading: true, showOkBtn: data?.handleType !== 'view' });
    userId.value = data?.userId;
    data && (pageParams = { ...data });
    await initRole(true);
    setModalProps({ confirmLoading: false, loading: false });
  });
  // 表单提交事件
  async function handleSubmit() {
    try {
      setModalProps({ confirmLoading: true, loading: true });
      const newRoles = getHaveDataSource()?.map((mapItems) => mapItems.roleId);
      // 获取当前已存在角色表格中所有id的集合
      const existingIds = new Set(haeRoles.value.map((p) => p.roleId));
      // 删除组：当前已存在角色表格中中id不在arr里的元素
      const toDelete = haeRoles.value.filter((p) => !newRoles.includes(p));
      // 新增组：arr中存在但arrParams中没有的id，创建对应对象
      const toAdd = newRoles.filter((id) => !existingIds.has(id)).map((id) => ({ id }));
      // 门店角色授权
      if (props.pageType === 'store') {
        await featchStoreRoleAuthorize({
          storeRoleAuthorize: [
            ...toAdd?.map((mapItems) => {
              return {
                authorizeLevel: pageParams?.authorizeLevel,
                roleId: mapItems.id,
                type: 1,
              };
            }),
            ...toDelete?.map((mapItems) => {
              return {
                authorizeLevel: pageParams?.authorizeLevel,
                roleId: mapItems,
                type: 2,
              };
            }),
          ],
        });
      } else {
        await bindRolesApi({
          roleAssignPermissionsList: [
            ...toAdd?.map((mapItems) => {
              return {
                userId: userId.value,
                roleId: mapItems.id,
                type: 1,
              };
            }),
            ...toDelete?.map((mapItems) => {
              return {
                userId: userId.value,
                roleId: mapItems,
                type: 2,
              };
            }),
          ],
        });
      }

      // 提交数据
      message.success(t('common.operation_successfull'));
      // 关闭弹窗
      closeModal();
      // 刷新列表
      emit('success');
    } finally {
      setModalProps({ confirmLoading: false, loading: false });
    }
  }
</script>
