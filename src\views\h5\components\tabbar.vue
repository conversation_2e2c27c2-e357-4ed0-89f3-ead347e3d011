<template>
  <Tabbar v-model="active" :z-index="999">
    <TabbarItem url="/mobile/h5Home" v-auth="'h5-home'">
      <span>{{ t('create_account') }}</span>
      <template #icon="props">
        <Icon name="wap-home-o" :color="props.active ? '#1990ff' : '#000000'" />
      </template>
    </TabbarItem>
    <TabbarItem url="/mobile/h5ToDoCenter" v-auth="'h5ToDoCenter'">
      <span>{{ t('todo_center') }}</span>
      <template #icon="props">
        <Icon name="sign" :color="props.active ? '#1990ff' : '#000000'" />
      </template>
    </TabbarItem>
    <!-- 这里的逻辑需要变更,要考虑权限的问题，然后来展示它的名称 -->
    <TabbarItem url="/mobile/h5Workbenches" v-auth="'h5Workbenches'">
      <span>{{ dynamicTabbarName ? t(dynamicTabbarName) : t('workbenches_text') }}</span>
      <template #icon="props">
        <Icon name="clock-o" :color="props.active ? '#1990ff' : '#000000'" />
      </template>
    </TabbarItem>
    <TabbarItem url="/mobile/h5PersonalCenter" v-auth="'h5PersonalCenter'">
      <span>{{ t('my_text') }}</span>
      <template #icon="props">
        <Icon name="user-o" :color="props.active ? '#1990ff' : '#000000'" />
      </template>
    </TabbarItem>
  </Tabbar>
</template>
<script lang="ts" setup name="h5Login">
  import { ref, onMounted, defineComponent } from 'vue';
  import { useRoute } from 'vue-router';
  import { Tabbar, TabbarItem, Icon } from 'vant';
  import { usePermission } from '/@/hooks/web/usePermission';
  import { useI18n } from '/@/hooks/web/useI18n';
  import 'vant/lib/index.css';

  defineComponent({
    components: {
      Tabbar,
      TabbarItem,
      Icon,
    },
  });

  const { t } = useI18n('common');

  // const router = useRouter();

  const active = ref(0);

  const dynamicTabbarName = ref(); // 动态的tabbar名称

  //按钮权限问题
  const { hasPermission } = usePermission();
  // 操作权限
  function hasImportAuth(auth) {
    return hasPermission(auth);
  }

  onMounted(() => {
    const route = useRoute();

    // 判断是否存在4个模块的权限 - 抽离到外层，所有路径都需要这个逻辑
    let haveAuthNums = [
      {
        url: `/gac_iop_childapp/clue-list-h5`,
        isBool: true,
        name: 'clue_management',
      },
      {
        url: '/gac_iop_childapp/work-order-list-h5',
        isBool: true,
        name: 'work_order_management',
      },
      {
        url: 'h5InvitationLinkManagement',
        isBool: true,
        name: 'invitation_link_management',
      },
      {
        url: 'h5AccountManage',
        isBool: true,
        name: 'account_management',
      },
    ];

    // 线索列表
    if (!hasImportAuth('clueTodoH5')) {
      haveAuthNums[0].isBool = false;
    }
    // 工单列表
    if (!hasImportAuth('workOrderTodoH5')) {
      haveAuthNums[1].isBool = false;
    }
    if (!hasImportAuth('h5InvitationLinkManagement')) {
      haveAuthNums[2].isBool = false;
    }
    if (!hasImportAuth('h5-accountManage')) {
      haveAuthNums[3].isBool = false;
    }

    // 设置动态 tabbar 名称 - 在所有路径判断之前处理
    const validAuthNums = haveAuthNums.filter((filterItems) => filterItems.isBool);
    if (validAuthNums.length === 1) {
      const findParams = validAuthNums[0];
      dynamicTabbarName.value = findParams?.name;
    } else {
      // 重置动态名称，使用默认文案
      dynamicTabbarName.value = null;
    }

    switch (route.path) {
      case '/mobile/h5Home':
        active.value = 0;
        break;
      case '/mobile/h5ToDoCenter':
        active.value = 1;
        break;
      case '/mobile/h5Workbenches': {
        /**
         * 如果4个只有一个权限-直接跳转页面
         */
        if (validAuthNums.length === 1) {
          const findParams = validAuthNums[0];
          /**
           * 这个场景是由于跳转 主应用-管理中心页的时候，如果只存在一个权限的时候，对于工单列表、线索列表的跳转需要特殊兼容处理，传递 backUrl 参数方便返回到一个固定的页面
           */
          const backUrl = '/mobile/h5ToDoCenter';
          window.location.href = `${findParams?.url}?backUrl=${encodeURIComponent(backUrl)}`;
        } else {
          active.value = 2;
        }
        break;
      }
      case '/mobile/h5PersonalCenter':
        active.value = 3;
        break;
    }
  });
</script>
<script lang="ts">
  export default {
    name: 'Tabbar',
    components: {
      Tabbar,
      TabbarItem,
      Icon,
    },
  };
</script>
<style scoped lang="less">
  ::v-deep(.van-tabbar-item__text) {
    text-align: center;
  }
</style>
