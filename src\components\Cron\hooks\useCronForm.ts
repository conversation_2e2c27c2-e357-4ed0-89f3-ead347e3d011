import { reactive, computed, watch, ComputedRef, onBeforeUnmount } from 'vue';
import { Form } from 'ant-design-vue';
import dayjs, { Dayjs } from 'dayjs';
import { calculateTriggerDateByRule, formatDateToString } from './useCronDateUtils';
import { REPEAT_TYPE_ENUM } from '/@/enums/common';
import { useI18n } from '/@/hooks/web/useI18n';
import { useLocale } from '/@/locales/useLocale';

// 测试功能开关：控制是否启用小时规则（仅用于测试）
// 设置为false可以一键禁用小时规则功能
export const ENABLE_HOUR_RULE_FOR_TESTING = false;

// 重复规则类型 小时规则用于测试
export type CronRuleType = 'day' | 'week' | 'month' | 'year' | 'hour';

// 表单状态类型
export interface FormState {
  repeatType: string; // 重复频率：不重复/重复
  startTime: Dayjs;
  repeatRuleType: CronRuleType; // 重复规则类型：天/周/月/年
  interval: number; // 重复间隔
  weekDays: number[]; // 选择的星期几（周日到周六：1-7，与Cron表达式一致）
  endTime: Dayjs; // 结束时间
  monthDays: number[]; // 月规则下指定的多个日期
}

// 基础日期格式
export const BASE_DAY_JS_FORMAT = 'YYYY-MM-DD HH:mm';

export const BASE_DAY_JS_FORMAT_WITH_SECONDS = 'YYYY-MM-DD HH:mm:ss';

// 组件属性类型
export interface CronProps {
  // 定时任务的结束时间，只对重复任务有效
  endTime?: string;
  cronExpression?: string;
  showTriggerRuleValidation?: boolean;
  originalStartTime?: string; // 定时任务的开始时间，用于解决时区计算问题
}

/**
 * 使用Cron表单
 * @param props 组件属性
 * @returns 表单状态、重复频率选项、重复规则类型选项、月份日期范围、间隔错误、规则类型错误、星期几选项、时间范围标签、基础日期格式、禁用过去的日期、禁用早于开始时间的日期、处理开始时间变更、处理重复频率变化、处理规则类型变化、处理月份日期的选择和取消选择、创建表单状态转换函数、计算实际的首次触发时间、监听repeatRuleType变化、监听表单状态变化、校验表单、日期选择器本地化配置
 */
export function useCronForm(props?: CronProps) {
  const { t } = useI18n('common');
  const { useForm } = Form;
  const { getAntdLocale } = useLocale() ?? {};

  const datePickerLocale = getAntdLocale?.value?.DatePicker ?? {};
  const timeRangeLabel = computed(() => datePickerLocale?.timePickerLocale?.rangePlaceholder);

  // 确定开始时间：优先使用原始开始时间，否则使用当前时间加一分钟
  let startTime: Dayjs;
  if (props?.originalStartTime) {
    startTime = dayjs(props.originalStartTime);
  } else {
    startTime = dayjs().add(1, 'minute');
  }

  // 确定结束时间
  let endTime: Dayjs;
  if (props?.endTime) {
    // 如果传入了endTime，使用传入的时间
    endTime = dayjs(props.endTime);
  } else {
    // 否则使用开始时间加一天
    endTime = startTime.clone().add(1, 'day');
  }

  // 表单状态初始化
  const formState = reactive<FormState>({
    repeatType: REPEAT_TYPE_ENUM.NO_REPEAT,
    startTime,
    repeatRuleType: 'day',
    interval: 1,
    weekDays: [2], // 默认选择周一，Cron表达式中周一为2
    endTime,
    monthDays: [1],
  });

  // 提取重复频率选项
  const REPEAT_OPTIONS = [
    { value: REPEAT_TYPE_ENUM.NO_REPEAT, label: t('noRepeat') },
    { value: REPEAT_TYPE_ENUM.REPEAT, label: t('customRepeat') },
  ];

  // 提取重复规则类型选项
  const REPEAT_RULE_TYPES = [
    // 小时规则选项（仅测试使用）- 根据开关控制是否显示
    ...(ENABLE_HOUR_RULE_FOR_TESTING ? [{ value: 'hour', label: t('time_hours') }] : []),
    { value: 'day', label: t('time_days') },
    { value: 'week', label: t('_week') },
    { value: 'month', label: t('months') },
    { value: 'year', label: datePickerLocale?.lang?.year },
  ];

  // 动态生成月份日期范围（保持最大31天）
  const monthDayRange = computed(() => Array.from({ length: 31 }, (_, i) => i + 1));

  // 表单错误状态
  const intervalError = computed(() => formState.repeatType === REPEAT_TYPE_ENUM.REPEAT && (!formState.interval || formState.interval < 1));

  const ruleTypeError = computed(() => formState.repeatType === REPEAT_TYPE_ENUM.REPEAT && !formState.repeatRuleType);

  // 选项数据 - 按Cron表达式顺序排列（1=周日，2=周一...7=周六）
  const weekDaysOptions = [t('sunday'), t('monday'), t('tuesday'), t('wednesday'), t('thursday'), t('friday'), t('saturday')];

  // 禁用过去的日期
  const disabledPastDate = (current: Dayjs) => {
    const now = dayjs();
    // 【测试功能】对于小时规则，对比到分钟级别，禁用过去的时间
    if (ENABLE_HOUR_RULE_FOR_TESTING) {
      return current && current.isBefore(now);
    }
    // 标准逻辑：按天比较
    return current && current.isBefore(now, 'day');
  };

  // 禁用早于开始时间的日期
  const disabledEndDate = (current: Dayjs) => {
    if (!current) return false;

    // 小时规则：只禁用早于开始时间的时间点（精确到分钟），允许选择当天
    if (ENABLE_HOUR_RULE_FOR_TESTING && formState.repeatRuleType === 'hour') {
      // 仅比较时间戳，只禁用早于开始时间的时间点，允许选择同一天
      return current.isBefore(formState.startTime);
    }

    // 其他规则：禁用同一天和早于开始时间的日期
    return current.isSame(formState.startTime, 'day') || current.isBefore(formState.startTime, 'day');
  };

  // 处理开始时间变更
  const handleStartTimeChange = (value: Dayjs) => {
    let newEndTime: Dayjs;

    // 【测试功能】根据规则类型决定默认的结束时间
    if (ENABLE_HOUR_RULE_FOR_TESTING && formState.repeatRuleType === 'hour') {
      // 小时规则下，默认结束时间设为开始时间加1小时，以便可以在同一天选择
      newEndTime = dayjs(value).add(1, 'hour');
    } else {
      // 标准逻辑：结束时间为开始时间加一天
      newEndTime = dayjs(value).add(1, 'day');
    }

    // 如果现有的结束时间早于或等于新的开始时间，则更新结束时间
    if (!formState.endTime || formState.endTime.isSame(value) || formState.endTime.isBefore(value)) {
      formState.endTime = newEndTime;
    }
  };

  // 校验结束时间
  const validateEndTime = async (_: unknown, value: Dayjs) => {
    if (!value) return Promise.reject(t('pleaseSelectEndTime'));

    if (!formState.startTime) {
      return Promise.reject(t('pleaseSelectStartTime'));
    }

    if (value.isBefore(formState.startTime)) {
      return Promise.reject(t('endTimeMustBeGreaterThanStartTime'));
    }

    return Promise.resolve();
  };

  // 使用计算属性定义表单验证规则，解决"Invalid watch source"警告
  const formRules = computed(() => ({
    startTime: [{ required: true, message: t('pleaseSelectStartTime') }],
    endTime: formState.repeatType === 'repeat' ? [{ required: true, message: t('pleaseSelectEndTime') }, { validator: validateEndTime }] : [],
    weekDays:
      formState.repeatType === 'repeat' && ['week', 'year'].includes(formState.repeatRuleType)
        ? [{ required: true, message: t('pleaseSelectWeek'), type: 'array', min: 1 }]
        : [],
    months:
      formState.repeatType === 'repeat' && ['month', 'year'].includes(formState.repeatRuleType)
        ? [{ required: true, message: datePickerLocale?.lang?.monthPlaceholder, type: 'array', min: 1 }]
        : [],
  }));

  // 设置校验规则并创建表单实例
  const { validate } = useForm(formState, formRules);

  // 处理重复频率变化
  const handleRepeatTypeChange = () => {
    if (formState.repeatType === REPEAT_TYPE_ENUM.NO_REPEAT) {
      // 重置重复相关的配置
      formState.repeatRuleType = 'day';
      formState.interval = 1;
      formState.weekDays = [2]; // 默认选择周一，Cron表达式中周一为2
      formState.monthDays = [1];
    }
  };

  // 处理规则类型变化
  const handleRuleTypeChange = () => {
    // 根据规则类型重置相关值
    if (ENABLE_HOUR_RULE_FOR_TESTING && formState.repeatRuleType === 'hour') {
      // 【测试功能】小时规则处理
      formState.weekDays = [2]; // 默认选择周一，Cron表达式中周一为2
      formState.monthDays = [1];

      // 如果当前有结束时间，对于小时规则切换时，设置结束时间为开始时间后1小时
      if (formState.startTime) {
        formState.endTime = formState.startTime.clone().add(1, 'hour');
      }
    } else if (formState.repeatRuleType === 'day') {
      formState.weekDays = [2]; // 默认选择周一，Cron表达式中周一为2
      formState.monthDays = [1];
    } else if (formState.repeatRuleType === 'week') {
      formState.weekDays = [2]; // 默认选择周一，Cron表达式中周一为2
      formState.monthDays = [1];
      // 周规则下，强制interval为1
      formState.interval = 1;
    } else if (formState.repeatRuleType === 'month') {
      formState.weekDays = [2]; // 默认选择周一，Cron表达式中周一为2
      // 保持 monthDays 不变，这样用户可以自行选择每月几号
    } else if (formState.repeatRuleType === 'year') {
      // 年规则不再需要选择星期几和月份
      formState.weekDays = [2]; // 默认选择周一，Cron表达式中周一为2
      formState.monthDays = [1];
    }
  };

  // 处理月份日期的选择和取消选择
  const toggleMonthDay = (day: number) => {
    const index = formState.monthDays.indexOf(day);
    if (index === -1) {
      // 如果不存在，添加到选中列表
      formState.monthDays.push(day);
    } else {
      // 如果已存在，从选中列表中移除
      // 但至少保留一个选中项
      if (formState.monthDays.length > 1) {
        formState.monthDays.splice(index, 1);
      }
    }
  };

  // 创建表单状态转换函数，用于调用核心计算函数
  const getFormStateForCalculation = () => ({
    repeatType: formState.repeatType === REPEAT_TYPE_ENUM.NO_REPEAT ? 'noRepeat' : 'repeat',
    repeatRuleType: formState.repeatRuleType,
    interval: formState.interval,
    weekDays: formState.weekDays,
    monthDays: formState.monthDays,
    startTime: formState.startTime,
    endTime: formState.endTime,
  });

  // 计算实际的首次触发时间
  const nextTriggerTime: ComputedRef<string> = computed(() => {
    // 对于"不重复"的情况，直接使用开始时间
    if (formState.repeatType === REPEAT_TYPE_ENUM.NO_REPEAT) {
      return formatDateToString(formState.startTime, weekDaysOptions);
    }

    // 使用核心计算函数获取首次触发时间
    const result = calculateTriggerDateByRule(getFormStateForCalculation(), 'nextTrigger', t);

    if (!result.isValid || !result.date) {
      return '';
    }

    // 格式化并返回计算出的触发时间
    return formatDateToString(result.date, weekDaysOptions);
  });

  // 监听repeatRuleType变化，当选择"week"时强制interval为1
  const watchRepeatRuleType = watch(
    () => formState.repeatRuleType,
    (newType) => {
      if (newType === 'week') {
        formState.interval = 1;
      }
    }
  );

  onBeforeUnmount(() => {
    watchRepeatRuleType();
  });

  return {
    formState,
    REPEAT_OPTIONS,
    REPEAT_RULE_TYPES,
    monthDayRange,
    intervalError,
    ruleTypeError,
    weekDaysOptions,
    timeRangeLabel,
    BASE_DAY_JS_FORMAT,
    disabledPastDate,
    disabledEndDate,
    handleStartTimeChange,
    handleRepeatTypeChange,
    handleRuleTypeChange,
    toggleMonthDay,
    getFormStateForCalculation,
    nextTriggerTime,
    validate,
    datePickerLocale,
  };
}
