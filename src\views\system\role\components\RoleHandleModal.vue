<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :width="660" :title="title" @ok="handleSubmit" destroyOnClose>
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<script lang="ts" setup>
  import { message } from 'ant-design-vue';
  import { computed, ref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { formSchema } from '../role.data';
  import { saveRoleApi, updateRoleApi } from '../role.api';
  import { useI18n } from '/@/hooks/web/useI18n';

  const { t } = useI18n();

  // 声明Emits
  const emit = defineEmits(['success', 'register']);

  const title = computed(() => {
    let titleVal = '';
    switch (handleType.value) {
      case 'add':
        titleVal = t('common.add');
        break;
      case 'edit':
        titleVal = t('common.edit');
        break;
    }
    return titleVal;
  });

  // 编辑、查看
  let eidtMenuId = '';
  // 操作类型
  let handleType = ref('');

  // 表单配置
  const [registerForm, { resetFields, setFieldsValue, clearValidate, validate, updateSchema }] = useForm({
    layout: 'vertical',
    schemas: formSchema,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
    labelWidth: '100%',
  });

  // 表单赋值
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    //重置表单
    await resetFields();
    setModalProps({ confirmLoading: true, loading: true });
    eidtMenuId = data?.roleId || '';
    handleType.value = data?.handleType || 'add';
    updateSchema(
      formSchema.map((mapItems) => {
        if (mapItems.field === 'roleType') {
          return {
            ...mapItems,
            dynamicDisabled: handleType.value === 'edit', // 编辑的时候禁用角色类型
          };
        }
        return {
          ...mapItems,
          dynamicDisabled: handleType.value === 'view',
        };
      })
    );
    //表单赋值git
    data?.handleType !== 'add' &&
      setFieldsValue({
        roleId: data?.roleId,
        roleName: data?.roleName || undefined,
        roleType: data?.roleType || undefined,
        remark: data?.remark || undefined,
        roleSign: data?.roleSign || undefined,
        region_country_code: data?.regionCode && data?.countryCode ? `${data.regionCode}_${data.countryCode}` : undefined,
      });
    clearValidate();
    setModalProps({ confirmLoading: false, loading: false });
  });
  // 表单提交事件
  async function handleSubmit() {
    try {
      const values = await validate();
      setModalProps({ confirmLoading: true, loading: true });
      const [regionCode, countryCode] = values.region_country_code?.split('_') || [];
      // 提交表单
      handleType.value === 'add'
        ? await saveRoleApi(values)
        : await updateRoleApi({
            ...values,
            regionCode: regionCode || '',
            countryCode: countryCode || '',
            roleId: eidtMenuId,
          });
      message.success(t('common.operation_successfull'));
      // 关闭弹窗
      closeModal();
      // 刷新列表
      emit('success');
    } finally {
      setModalProps({ confirmLoading: false, loading: false });
    }
  }
</script>
