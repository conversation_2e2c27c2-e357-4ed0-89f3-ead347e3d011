<template>
  <div :class="prefixCls" class="login-background-img">
    <div class="flex items-center absolute top-4 right-4">
      <a-button style="margin-right: 20px" type="link" @click="viewAPIDocumentation">API Documentation</a-button>
      <AppLocalePicker :reload="true" class="enter-x xl:text-gray-600" :showText="true" @language-res="getLanguageRes" />
    </div>
    <div class="aui-logo" v-if="!getIsMobile">
      <div>
        <h3>
          <img :src="logoImg" alt="jeecg" />
        </h3>
      </div>
    </div>
    <div v-else class="aui-phone-logo">
      <img :src="logoImg" alt="jeecg" />
    </div>
    <div>
      <div class="aui-content">
        <div class="aui-container flex">
          <div class="logo_text">
            <div style="font-size: 58px; font-weight: bolder; color: #000000"
              >广汽国际用户运营平台
              <div style="font-size: 26px">GAC International User Operation Platform</div>
            </div>
          </div>
          <div class="aui-form">
            <div class="aui-formBox">
              <div class="aui-formWell">
                <div class="investment_title flex">
                  <div :class="loginType === 1 ? 'select' : ''" @click="changeLoginType(1)">{{ t('common.phone') }} </div>
                  <div class="email" :class="loginType === 2 ? 'select' : ''" @click="changeLoginType(2)">{{ t('common.email_text') }}</div>
                </div>
                <div class="aui-form-box">
                  <a-form ref="loginRef" :model="formData" @keyup.enter.native="accountLogin">
                    <div class="aui-account">
                      <div class="aui-inputClear">
                        <a-form-item>
                          <div style="border-radius: 20px 0px 0px 20px; overflow: hidden">
                            <div class="flex" v-if="loginType === 1">
                              <a-select
                                style="width: 168px"
                                v-model:value="formData.quhao"
                                :showSearch="true"
                                :placeholder="t('common.global_roaming')"
                                :options="quhaoOptions"
                              />
                              <div style="width: 1px; height: 65px; background: #c6cbd0"></div>
                              <a-input
                                autocomplete="off"
                                class="fix-auto-fill"
                                :placeholder="t('common.mobilePlaceholder')"
                                v-model:value="formData.username"
                              />
                            </div>
                            <div class="flex" v-else>
                              <a-input
                                autocomplete="off"
                                class="fix-auto-fill"
                                :placeholder="t('common.pleaseEnterEmail')"
                                v-model:value="formData.username"
                              />
                            </div>
                          </div>
                        </a-form-item>
                      </div>
                      <div class="aui-inputClear">
                        <a-form-item>
                          <a-input
                            class="fix-auto-fill"
                            type="password"
                            :placeholder="t('common.passwordPlaceholder')"
                            v-model:value="formData.password"
                          />
                        </a-form-item>
                      </div>
                      <div class="aui-inputClear">
                        <a-form-item>
                          <a-input
                            class="fix-auto-fill"
                            type="text"
                            :placeholder="t('common.inputCodePlaceholder')"
                            v-model:value="formData.inputCode"
                          />
                        </a-form-item>
                        <div class="aui-code" :class="{ 'aui-code-arabic': unref(getLocale) === 'ar-AR' }">
                          <span v-show="isShowGetCode === true" style="color: #1990ff; font-size: 14px" @click.stop="handleChangeCheckCode">{{
                            t('common.obtain_verification_code_text')
                          }}</span>
                          <StatisticCountdown
                            v-show="isShowGetCode === false"
                            :valueStyle="{ color: '#1990FF', fontSize: '18px' }"
                            :value="timeDjs"
                            :format="`ss${t('common.retrieveSsSecondsLater')}`"
                            @finish="isShowGetCode = true"
                          />
                        </div>
                      </div>
                    </div>
                    <div style="cursor: pointer; margin-top: 10px; display: flex; justify-content: flex-end">
                      <span @click.stop="toForgetPassword">{{ t('common.static_state_route_forget_password') }}</span>
                    </div>
                    <div class="aui-formButton">
                      <a-button :loading="loginLoading" class="aui-link-login" type="primary" @click="accountLogin">{{
                        t('common.loginButton')
                      }}</a-button>
                    </div>
                  </a-form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <ForgetUpdatePassword v-if="passwordVisible" ref="updatePasswordRef" />
  </div>
</template>
<script lang="ts" setup name="login-mini">
  import { reactive, ref, toRaw, unref, onMounted } from 'vue';
  import { StatisticCountdown } from 'ant-design-vue';
  import { useRouter } from 'vue-router';
  import { PageEnum } from '/@/enums/pageEnum';
  import { useUserStore } from '/@/store/modules/user';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useI18n } from '/@/hooks/web/useI18n';
  import logoImg from '/@/assets/images/logo.png';
  import { AppLocalePicker } from '/@/components/Application';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { useAppInject } from '/@/hooks/web/useAppInject';
  import { useLocale } from '/@/locales/useLocale';
  import { sendLoginMsgApi, queryAreaListApi } from '/@/api/common/api';
  import ForgetUpdatePassword from './forgetPasswordModal.vue';
  import { LOCALE } from '/@/settings/localeSetting';
  import { getRefPromise } from '/@/utils/index';
  import { EmailRegex } from '/@/utils/helper/validator';
  import rsaEncryptor from '/@/utils/rsaEncrypt';

  const { prefixCls } = useDesign('mini-login');
  const { notification, createMessage } = useMessage();
  const userStore = useUserStore();
  const { t } = useI18n();

  const router = useRouter();

  const { getLocale } = useLocale();

  // 验证码倒计时
  const timeDjs = ref(Date.now() + 1000 * 60);

  // 当前登录类型-1手机号  2邮箱
  const loginType = ref(1);
  const changeLoginType = (type) => {
    formData.username = null;
    loginType.value = type;
  };

  // 区号数据
  const quhaoOptions = ref([]);

  // 获取国际区号
  async function queryAreaListFn() {
    try {
      const res = await queryAreaListApi();
      quhaoOptions.value =
        res?.map((mapItems) => {
          return {
            label: mapItems.areaCode,
            value: mapItems.areaCode,
          };
        }) || [];
    } catch (err) {}
  }

  const newLanguageData = ref([] as any);
  function getLanguageRes(data) {
    newLanguageData.value = data?.map((mapItems) => {
      return {
        label: mapItems.text,
        value: mapItems.event,
      };
    });
  }

  // 忘记密码弹窗
  const passwordVisible = ref(false);
  const updatePasswordRef = ref();
  // 点击忘记密码-弹窗打开
  async function toForgetPassword() {
    passwordVisible.value = true;
    await getRefPromise(updatePasswordRef);
    updatePasswordRef.value.show();
  }

  // 查看API文档
  const viewAPIDocumentation = () => {
    window.open(router.resolve({ path: PageEnum.API_DOCUMENT }).href, '_blank');
  };

  // 是否展示重新获取验证码
  const isShowGetCode = ref(true);
  const loading = ref(false);

  //账号登录表单字段
  const formData = reactive<any>({
    inputCode: '',
    quhao: null,
    username: '',
    password: '',
    loginSite: 1,
    language: unref(getLocale),
    LOCALE,
  });
  const loginRef = ref();
  const loginLoading = ref<boolean>(false);
  const { getIsMobile } = useAppInject();
  defineProps({
    sessionTimeout: {
      type: Boolean,
    },
  });

  /**
   * 获取验证码
   */
  async function handleChangeCheckCode() {
    if (loginType.value === 1 && !formData.quhao) {
      createMessage.warn(`${t('common.chooseText')}${t('common.global_roaming')}`);
      return;
    }
    if (!formData.username) {
      createMessage.warn(loginType.value === 1 ? t('common.mobilePlaceholder') : t('common.pleaseEnterEmail'));
      return;
    }
    // 邮箱验证
    if (loginType.value === 2) {
      if (!EmailRegex.test(formData.username)) {
        createMessage.warn(t('common.enterFormatEmail'));
        return;
      }
    }
    if (!formData.language) {
      createMessage.warn(t('common.please_select_systemLanguage'));
      return;
    }
    if (loading.value) {
      return;
    }
    loading.value = true;
    await sendLoginMsgApi({
      account: loginType.value === 1 ? formData.quhao + formData.username : formData.username,
      language: formData.language,
    });
    createMessage.success(t('common.verification_code_sent_successfully'));
    isShowGetCode.value = false;
    setTimeout(() => {
      loading.value = false;
    }, 2000);
    timeDjs.value = Date.now() + 1000 * 60;
  }

  /**
   * 账号或者手机登录
   */
  async function accountLogin() {
    if (loginType.value === 1 && !formData.quhao) {
      createMessage.warn(`${t('common.chooseText')}${t('common.global_roaming')}`);
      return;
    }
    if (!formData.username) {
      createMessage.warn(loginType.value === 1 ? t('common.mobilePlaceholder') : t('common.pleaseEnterEmail'));
      return;
    }
    // 邮箱验证
    if (loginType.value === 2) {
      if (!EmailRegex.test(formData.username)) {
        createMessage.warn(t('common.enterFormatEmail'));
        return;
      }
    }
    if (!formData.password) {
      createMessage.warn(t('common.passwordPlaceholder'));
      return;
    }
    if (!formData.inputCode) {
      createMessage.warn(t('common.inputCodePlaceholder'));
      return;
    }
    try {
      loginLoading.value = true;
      // @ts-ignore
      const { userInfo } = await userStore.login(
        toRaw({
          account: loginType.value === 1 ? formData.quhao + formData.username : formData.username,
          password: rsaEncryptor.encrypt(formData.password),
          code: formData.inputCode,
        })
      );
      if (userInfo) {
        notification.success({
          message: t('common.loginSuccessTitle'),
          description: `${t('common.loginSuccessDesc')}: ${userInfo.realname}`,
          duration: 3,
        });
      }
    } catch (error) {
      notification.error({
        message: t('common.api_errorTip'),
        // @ts-ignore
        description: error.message || t('common.networkExceptionMsg'),
        duration: 3,
      });
    } finally {
      loginLoading.value = false;
    }
  }

  onMounted(() => {
    queryAreaListFn();
  });
</script>

<style lang="less" scoped>
  @import '/@/assets/loginmini/style/home.less';
  @import '/@/assets/loginmini/style/base.less';

  .investment_title {
    font-size: 28px;
    > div {
      position: relative;
      color: #b8b8b8;
      min-width: 96px;
      // height: 60px;
      text-align: center;
      cursor: pointer;
      padding: 0 0 20px 0;
      &.select {
        color: #000000;
        &::before {
          position: absolute;
          left: 50%;
          bottom: 0;
          content: '';
          display: block;
          width: 52px;
          height: 8px;
          margin-left: -26px;
          background: #1990ff;
          border-radius: 20px 20px 20px 20px;
        }
      }
    }
    .email {
      margin-left: 44px;
    }
  }

  ::v-deep .ant-select-selection-item {
    display: flex;
    align-items: center;
  }
  @media (max-width: 1400px) {
    .aui-container {
      display: initial;
    }
    .aui-form {
      width: 100%;
    }
    .aui-container > .logo_text {
      display: flex;
      justify-content: center;
    }
  }
  @media (max-width: 500px) {
    .aui-container {
      display: initial;
    }
    .aui-form {
      width: 100%;
    }
    .aui-container > .logo_text {
      display: none;
    }
  }
  :deep(.ant-input:focus) {
    box-shadow: none;
  }
  .aui-get-code {
    float: right;
    position: relative;
    z-index: 3;
    background: #ffffff;
    color: #1573e9;
    border-radius: 100px;
    padding: 5px 16px;
    margin: 7px;
    border: 1px solid #1573e9;
    top: 12px;
  }

  .aui-get-code:hover {
    color: #1573e9;
  }

  .code-shape {
    border-color: #dadada !important;
    color: #aaa !important;
  }

  :deep(.jeecg-dark-switch) {
    position: absolute;
    margin-right: 10px;
  }

  .aui-link-login {
    width: 100%;
    height: 65px;
    padding: 10px 15px;
    font-size: 18px;
    border-radius: 20px;
    margin-top: 40px;
    flex: 1;
    color: #fff;
  }
  .language-form {
    margin-left: 12px;
  }
  :deep(.ant-select-selector) {
    height: 65px !important;
    background-color: #f7f8fa !important;
    border: none !important;
  }
  :deep(.ant-select-selection-search-input) {
    height: 68px !important;
  }
  :deep(.ant-select-selection-placeholder) {
    line-height: 68px !important;
  }
  .select-every {
    width: 100%;
  }
  .aui-phone-logo {
    position: absolute;
    margin-left: 10px;
    width: 60px;
    top: 2px;
    z-index: 4;
  }
  .top-3 {
    top: 0.45rem;
  }

  .aui-code-arabic {
    position: absolute;
    right: 70%;
  }
</style>

<style lang="less">
  @prefix-cls: ~'@{namespace}-mini-login';
  @dark-bg: #293146;

  html[data-theme='dark'] {
    .@{prefix-cls} {
      background-color: @dark-bg !important;
      background-image: none;

      &::before {
        background-image: url(/@/assets/svg/login-bg-dark.svg);
      }
      .aui-inputClear {
        background-color: #232a3b !important;
      }
      .ant-input,
      .ant-input-password {
        background-color: #232a3b !important;
      }

      .ant-btn:not(.ant-btn-link):not(.ant-btn-primary) {
        border: 1px solid #4a5569 !important;
      }

      &-form {
        background: @dark-bg !important;
      }

      .app-iconify {
        color: #fff !important;
      }
      .aui-inputClear input,
      .aui-input-line input,
      .aui-choice {
        color: #c9d1d9 !important;
      }

      .aui-formBox {
        background-color: @dark-bg !important;
      }
      .aui-third-text span {
        background-color: @dark-bg !important;
      }
      .aui-form-nav .aui-flex-box {
        color: #c9d1d9 !important;
      }

      .aui-formButton .aui-linek-code {
        background: @dark-bg !important;
        color: white !important;
      }
      .aui-code-line {
        border-left: none !important;
      }
      .ant-checkbox-inner,
      .aui-success h3 {
        border-color: #c9d1d9;
      }
      //update-begin---author:wangshuai ---date:20230828  for：【QQYUN-6363】这个样式代码有问题，不在里面，导致表达式有问题------------
      &-sign-in-way {
        .anticon {
          font-size: 22px !important;
          color: #888 !important;
          cursor: pointer !important;

          &:hover {
            color: @primary-color !important;
          }
        }
      }
      //update-end---author:wangshuai ---date:20230828  for：【QQYUN-6363】这个样式代码有问题，不在里面，导致表达式有问题------------
    }

    input.fix-auto-fill,
    .fix-auto-fill input {
      -webkit-text-fill-color: #c9d1d9 !important;
      box-shadow: inherit !important;
    }

    .ant-divider-inner-text {
      font-size: 12px !important;
      color: @text-color-secondary !important;
    }
    .aui-third-login a {
      background: transparent;
    }
  }
</style>
