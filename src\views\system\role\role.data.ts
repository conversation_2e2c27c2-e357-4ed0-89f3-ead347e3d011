import { FormSchema } from '/@/components/Table';
import { queryRegionCountryApi } from '/@/views/system/depart/depart.api';
import { useI18n } from '/@/hooks/web/useI18n';
import { RoleTypeOptions } from '@/enums/roleEnum';
import { getLabelFromDict } from '@ruqi/utils-admin';

const { t } = useI18n();

// 角色列表
export const columns = [
  {
    title: t('common.role_id_text'),
    dataIndex: 'roleId',
    width: 100,
    customRender: ({ text }) => {
      return `ROLE00${text}`;
    },
  },
  {
    title: t('common.role_name_text'),
    dataIndex: 'roleName',
    width: 100,
  },
  {
    title: t('common.roleType'),
    dataIndex: 'roleType',
    width: 120,
    customRender: ({ text }) => getLabelFromDict(text, RoleTypeOptions),
  },
  {
    title: t('common.auditRemark'),
    dataIndex: 'remark',
    width: 100,
  },
  {
    title: t('common.status'),
    dataIndex: 'status',
    width: 120,
    customRender: ({ text }) => {
      return text === 1 ? t('common.enableText') : t('common.disableText');
    },
  },
  {
    title: t('common.region_country_text'),
    dataIndex: 'region_country_code',
    width: 120,
  },
  {
    title: t('common.createTime'),
    dataIndex: 'createTime',
    width: 120,
  },
  {
    title: t('common.updateTime'),
    dataIndex: 'updateTime',
    width: 120,
  },
];

// 角色列表搜索
export const searchFormSchema: FormSchema[] = [
  // {
  //   field: 'roleId',
  //   label: t('common.role_id_text'),
  //   component: 'Input',
  //   colProps: { span: 6 },
  //   dynamicRules: () => {
  //     return [{ pattern: /^[1-9]\d*$/, message: t('common.pleaseEnterPositiveInteger'), trigger: 'blur' }];
  //   },
  // },
  {
    field: 'roleName',
    label: t('common.role_name_text'),
    component: 'Input',
    labelWidth: '100%',
  },
  {
    field: 'roleType',
    label: t('common.roleType'),
    component: 'Select',
    labelWidth: '100%',
    componentProps: {
      options: RoleTypeOptions,
    },
  },
];

// 新增、编辑角色
export const formSchema: FormSchema[] = [
  {
    field: 'roleId',
    label: t('common.role_id_text'),
    component: 'Input',
    show: false,
  },
  {
    field: 'roleName',
    label: t('common.role_name_text'),
    required: true,
    component: 'Input',
  },
  {
    field: 'roleType',
    label: t('common.roleType'),
    required: true,
    component: 'Select',
    componentProps: {
      options: RoleTypeOptions,
    },
  },
  {
    label: t('common.region_country_text'),
    field: 'region_country_code',
    component: 'ApiTreeSelect',
    labelWidth: '100%',
    componentProps: {
      showSearch: true,
      api: queryRegionCountryApi,
      customReturnFn: (res) => {
        return (
          res?.map((mapItems) => {
            return {
              label: mapItems.region,
              value: mapItems.regionCode,
              disabled: true,
              children:
                mapItems.countries?.map((mapItem) => {
                  return {
                    label: mapItem.regionCountry,
                    value: `${mapItems.regionCode}_${mapItem.regionCountryCode}`,
                  };
                }) || undefined,
            };
          }) || []
        );
      },
      dropdownStyle: { maxHeight: '200px', overflow: 'auto' },
      getPopupContainer: () => document.body,
    },
  },
  {
    label: t('common.auditRemark'),
    field: 'remark',
    component: 'InputTextArea',
  },
  {
    field: 'roleSign',
    label: t('common.role_label'),
    component: 'Input',
    componentProps: {
      placeholder: t('common.pleaseInputRoleTag'),
    },
  },
];

// 新增、编辑角色
export const assignPermissionsFormSchema: FormSchema[] = [
  {
    field: 'roleId',
    label: t('common.role_id_text'),
    component: 'Input',
    show: false,
  },
  {
    field: 'roleName',
    label: t('common.role_name_text'),
    required: true,
    component: 'Input',
  },
  {
    label: t('common.region_country_text'),
    field: 'region_country_code',
    component: 'ApiTreeSelect',
    componentProps: {
      showSearch: true,
      api: queryRegionCountryApi,
      customReturnFn: (res) => {
        return (
          res?.map((mapItems) => {
            return {
              label: mapItems.region,
              value: mapItems.regionCode,
              disabled: true,
              children:
                mapItems.countries?.map((mapItem) => {
                  return {
                    label: mapItem.regionCountry,
                    value: `${mapItems.regionCode}_${mapItem.regionCountryCode}`,
                  };
                }) || undefined,
            };
          }) || []
        );
      },
      dropdownStyle: { maxHeight: '200px', overflow: 'auto' },
      getPopupContainer: () => document.body,
    },
  },
  {
    field: 'status',
    label: t('common.role_status_text'),
    required: true,
    component: 'Select',
    componentProps: {
      options: [
        {
          label: t('common.disableText'),
          value: '2',
        },
        {
          label: t('common.enableText'),
          value: '1',
        },
      ],
    },
  },
  {
    field: 'roleSign',
    label: t('common.role_label'),
    component: 'Input',
    componentProps: {
      placeholder: t('common.pleaseInputRoleTag'),
    },
  },
  {
    label: '',
    component: 'Input',
    field: 'treeRole',
    slot: 'treeRoleSlots',
    colProps: { sm: 24, xs: 24, xl: 24, xxl: 24, md: 24, lg: 24 },
  },
  {
    label: t('common.auditRemark'),
    field: 'remark',
    component: 'InputTextArea',
    colProps: { span: 24 },
  },
];
