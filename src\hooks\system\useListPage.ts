import { reactive, ref, Ref, unref } from 'vue';
import { merge } from 'lodash-es';
import { DynamicProps } from '/#/utils';
import { BasicTableProps, PaginationProps, TableActionType, useTable } from '/@/components/Table';
import { ColEx } from '/@/components/Form/src/types';
import { FormActionType } from '/@/components/Form';
import { useMessage } from '/@/hooks/web/useMessage';
import { useMethods } from '/@/hooks/system/useMethods';
import { useDesign } from '/@/hooks/web/useDesign';
import { filterObj } from '/@/utils/common/compUtils';
import { awaitTo } from '@ruqi/utils-admin';
import { isString, isArray } from '/@/utils/is';
import { getObsTemporaryUrls, ObsTemporaryType, uploadFile } from '/@/api/obs';
const { handleExportXls } = useMethods();
import { useI18n } from '/@/hooks/web/useI18n';

const { t } = useI18n();

// 定义 useListPage 方法所需参数
interface ListPageOptions {
  /** 样式作用域范围 */
  designScope?: string;
  /**【必填】表格参数配置 */
  tableProps: TableProps;
  /** 是否分页 */
  pagination?: boolean;
  /** 导出配置 */
  exportConfig?: {
    url: string | (() => string);
    // 导出文件名
    name?: string | (() => string);
    //导出参数
    params?: object;
    methods?: string;
  };
  /** 导入配置 */
  importConfig?: {
    //update-begin-author:taoyan date:20220507 for: erp代码生成 子表 导入地址是动态的
    url: any;
    //update-end-author:taoyan date:20220507 for: erp代码生成 子表 导入地址是动态的
    /** 导出成功后的回调 */
    success?: (fileInfo?: any) => void;
    /** 导出失败后的回调 */
    fail?: (fileInfo?: any) => void;
    isSuccessCustomTip?: boolean;
  };
}

interface IDoRequestOptions {
  // 是否显示确认对话框，默认 true
  confirm?: boolean;
  // 是否自动刷新表格，默认 true
  reload?: boolean;
  // 是否自动清空选择，默认 true
  clearSelection?: boolean;
}

type onExportXlsConfig = {
  beforeExportXls: (params: Recordable) => void; // 导出前处理
};

/**
 * listPage页面公共方法
 *
 * @param options
 */
export function useListPage(options: ListPageOptions) {
  const $message = useMessage();
  let $design = {} as ReturnType<typeof useDesign>;
  if (options.designScope) {
    $design = useDesign(options.designScope);
  }

  const tableContext = useListTable(options.tableProps);

  const [, { getForm, reload, setLoading, getPaginationRef }, { selectedRowKeys, selectedRows }] = tableContext;

  // 导出 excel
  async function onExportXls(config?: onExportXlsConfig) {
    //update-begin---author:wangshuai ---date:20220411  for：导出新增自定义参数------------
    const { url, name, params, methods } = options?.exportConfig ?? {};
    const paginationRef = getPaginationRef() as PaginationProps;
    const { beforeExportXls } = config ?? {};
    const realUrl = typeof url === 'function' ? url() : url;
    if (realUrl) {
      const title = typeof name === 'function' ? name() : name;
      //update-begin-author:taoyan date:20220507 for: erp代码生成 子表 导出报错，原因未知-
      let paramsForm: any = {};
      try {
        paramsForm = await getForm().validate();
      } catch (e) {
        console.error(e);
      }
      //update-end-author:taoyan date:20220507 for: erp代码生成 子表 导出报错，原因未知-

      //如果参数不为空，则整合到一起
      //update-begin-author:taoyan date:20220507 for: erp代码生成 子表 导出动态设置mainId
      if (params) {
        Object.keys(params).map((k) => {
          const temp = (params as object)[k];
          if (temp) {
            paramsForm[k] = unref(temp);
          }
        });
      }
      //update-end-author:taoyan date:20220507 for: erp代码生成 子表 导出动态设置mainId
      if (selectedRowKeys.value && selectedRowKeys.value.length > 0) {
        paramsForm['selections'] = selectedRowKeys.value.join(',');
        paramsForm['selectionRows'] = selectedRows.value;
      }

      if (paginationRef) {
        // 合并分页参数
        paramsForm = Object.assign(paramsForm, {
          pageSize: paginationRef.pageSize,
          pageIndex: paginationRef.current,
        });
      }

      // 导出前处理
      beforeExportXls && (paramsForm = Object.assign(paramsForm, beforeExportXls(paramsForm)));

      return handleExportXls(title as string, realUrl, filterObj(paramsForm), methods);
      //update-end---author:wangshuai ---date:20220411  for：导出新增自定义参数--------------
    } else {
      $message.createMessage.warn('没有传递 exportConfig.url 参数');
      return Promise.reject();
    }
  }

  // 华为云文件目录
  const publicUrl = 'public/';
  // 导入 excel
  async function onImportXls(file) {
    const timeStampFilePath = publicUrl + `${Date.now()}-${file.file.name}`;
    const [err, res] = await awaitTo(
      getObsTemporaryUrls({
        type: ObsTemporaryType.Upload, //0是下载，1是上传
        filePath: timeStampFilePath,
        expires: 630720000,
      })
    );
    if (err) {
      $message.createMessage.error('common.uploadFail');
      return;
    }
    if (res?.content) {
      const { url: apiUrl, success, fail, isSuccessCustomTip = false } = options?.importConfig ?? {};
      const fileUrl = res.content;
      const reader = new FileReader();
      reader.readAsArrayBuffer(file.file as unknown as File);
      reader.onload = async (e) => {
        const binaryData = e.target?.result;
        const [err1] = await awaitTo(uploadFile(fileUrl, binaryData as ArrayBuffer));
        if (err1) {
          $message.createMessage.error('common.uploadFail');
          fail && fail(err1);
          return;
        }
        const [err2, res2] = await awaitTo(
          getObsTemporaryUrls({
            type: ObsTemporaryType.Download, //0是下载，1是上传
            filePath: timeStampFilePath,
            expires: 630720000, //单位是秒，默认不传给后端就是10分钟
          })
        );
        if (err2) {
          $message.createMessage.error('common.uploadFail');
          fail && fail(err2);
          return;
        }
        if (res2?.content) {
          if (apiUrl) {
            try {
              await apiUrl(
                {
                  // fileUrl: res2.content,
                  fileUrl: timeStampFilePath,
                },
                success
              );
              !isSuccessCustomTip && $message.createMessage.success(t('common.importSuccess'));
            } catch (err) {
              $message.createMessage.error(t('common.importFail'));
              fail && fail(err);
            }
          } else {
            $message.createMessage.warn('没有传递 importConfig.url 参数');
            return Promise.reject();
          }
        }
      };
    }
    // //update-begin-author:taoyan date:20220507 for: erp代码生成 子表 导入地址是动态的
  }

  /**
   * 通用请求处理方法，可自动刷新表格，自动清空选择
   * @param api 请求api
   * @param options 是否显示确认框
   */
  function doRequest(api: () => Promise<any>, options?: IDoRequestOptions) {
    return new Promise((resolve, reject) => {
      const execute = async () => {
        try {
          setLoading(true);
          const res = await api();
          if (options?.reload ?? true) {
            reload();
          }
          if (options?.clearSelection ?? true) {
            selectedRowKeys.value = [];
          }
          resolve(res);
        } catch (e) {
          reject(e);
        } finally {
          setLoading(false);
        }
      };
      if (options?.confirm ?? true) {
        $message.createConfirm({
          iconType: 'warning',
          title: '删除',
          content: '确定要删除吗？',
          onOk: () => execute(),
          onCancel: () => reject(),
        });
      } else {
        execute();
      }
    });
  }

  /** 执行单个删除操作 */
  function doDeleteRecord(api: () => Promise<any>) {
    return doRequest(api, { confirm: false, clearSelection: false });
  }

  return {
    ...$design,
    ...$message,
    onExportXls,
    onImportXls,
    doRequest,
    doDeleteRecord,
    tableContext,
  };
}

// 定义表格所需参数
type TableProps = Partial<DynamicProps<BasicTableProps>>;
type UseTableMethod = TableActionType & {
  getForm: () => FormActionType;
};

/**
 * useListTable 列表页面标准表格参数
 *
 * @param tableProps 表格参数
 */
export function useListTable(tableProps: TableProps): [
  (instance: TableActionType, formInstance: UseTableMethod) => void,
  TableActionType & {
    getForm: () => FormActionType;
  },
  {
    rowSelection: any;
    selectedRows: Ref<Recordable[]>;
    selectedRowKeys: Ref<any[]>;
  },
] {
  // 自适应列配置
  const adaptiveColProps: Partial<ColEx> = {
    xs: 24, // <576px
    sm: 12, // ≥576px
    md: 12, // ≥768px
    lg: 8, // ≥992px
    xl: 8, // ≥1200px
    xxl: 6, // ≥1600px
  };
  const defaultTableProps: TableProps = {
    rowKey: 'id',
    // 使用查询条件区域
    useSearchForm: true,
    // 查询条件区域配置
    formConfig: {
      // 紧凑模式
      compact: true,
      // label默认宽度
      // labelWidth: 120,
      // 按下回车后自动提交
      autoSubmitOnEnter: true,
      // 默认 row 配置
      rowProps: { gutter: 8 },
      // 默认 col 配置
      baseColProps: {
        ...adaptiveColProps,
      },
      labelCol: {
        xs: 24,
        sm: 8,
        md: 6,
        lg: 8,
        xl: 6,
        xxl: 6,
      },
      wrapperCol: {},
      // 是否显示 展开/收起 按钮
      showAdvancedButton: true,
      // 超过指定列数默认折叠
      autoAdvancedCol: 3,
      // 操作按钮配置
      actionColOptions: {
        ...adaptiveColProps,
        style: { textAlign: 'left' },
      },
    },
    // 斑马纹
    striped: false,
    // 是否可以自适应高度
    canResize: true,
    // 表格最小高度
    // update-begin--author:liaozhiyang---date:20240603---for【TV360X-861】列表查询区域不可往上滚动
    minHeight: 300,
    // update-end--author:liaozhiyang---date:20240603---for【TV360X-861】列表查询区域不可往上滚动
    // 点击行选中
    clickToRowSelect: false,
    // 是否显示边框
    bordered: true,
    // 是否显示序号列
    showIndexColumn: false,
    // 显示表格设置
    showTableSetting: true,
    // 表格全屏设置
    tableSetting: {
      fullScreen: false,
    },
    // 是否显示操作列
    showActionColumn: true,
    // 操作列
    actionColumn: {
      width: 120,
      title: t('common.operation'),
      //是否锁定操作列取值 right ,left,false
      fixed: 'right',
      dataIndex: 'action',
      slots: { customRender: 'action' },
    },
  };
  // 合并用户个性化配置
  if (tableProps) {
    //update-begin---author:wangshuai---date:2024-04-28---for:【issues/6180】前端代码配置表变查询条件显示列不生效---
    if (tableProps.formConfig) {
      setTableProps(tableProps.formConfig);
    }
    if (isArray(tableProps.columns)) {
      tableProps.columns.forEach((item) => {
        item.resizable = true;
        item.minWidth = item.width ? Number(item.width) : 120;
      });
    }
    //update-end---author:wangshuai---date:2024-04-28---for:【issues/6180】前端代码配置表变查询条件显示列不生效---
    // merge 方法可深度合并对象
    merge(defaultTableProps, tableProps);
  }

  // 合并方法
  Object.assign(defaultTableProps);
  if (typeof tableProps.beforeFetch === 'function') {
    defaultTableProps.beforeFetch = function (params) {
      // @ts-ignore
      const customForm = tableProps.beforeFetch(params);
      params = {
        ...params,
        ...(customForm || {}),
      };
      return params;
    };
  }

  // 当前选择的行
  const selectedRowKeys = ref<any[]>([]);
  // 选择的行记录
  const selectedRows = ref<Recordable[]>([]);

  // 表格选择列配置
  const rowSelection: any = tableProps?.rowSelection ?? {};
  const defaultRowSelection = reactive({
    ...rowSelection,
    type: rowSelection.type ?? 'checkbox',
    fixed: 'left',
    // 选择列宽度，默认 50
    columnWidth: rowSelection.columnWidth ?? 50,
    selectedRows: selectedRows,
    selectedRowKeys: selectedRowKeys,
    onChange(...args) {
      selectedRowKeys.value = args[0];
      selectedRows.value = args[1];
      if (typeof rowSelection.onChange === 'function') {
        rowSelection.onChange(...args);
      }
    },
  });
  delete defaultTableProps.rowSelection;

  /**
   * 设置表格参数
   *
   * @param formConfig
   */
  function setTableProps(formConfig: any) {
    const replaceAttributeArray: string[] = ['baseColProps', 'labelCol'];
    for (const item of replaceAttributeArray) {
      if (formConfig && formConfig[item]) {
        if (defaultTableProps.formConfig) {
          const defaultFormConfig: any = defaultTableProps.formConfig;
          defaultFormConfig[item] = formConfig[item];
        }
        formConfig[item] = {};
      }
    }
  }

  return [
    ...useTable(defaultTableProps),
    {
      selectedRows,
      selectedRowKeys,
      rowSelection: defaultRowSelection,
    },
  ];
}
