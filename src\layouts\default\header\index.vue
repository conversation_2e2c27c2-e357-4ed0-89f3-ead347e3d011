<template>
  <Header :class="getHeaderClass">
    <!-- left start -->
    <div :class="`${prefixCls}-left`">
      <!-- logo -->
      <AppLogo v-if="getShowHeaderLogo || getIsMobile" :class="`${prefixCls}-logo`" :theme="getHeaderTheme" :style="getLogoWidth" />
      <LayoutTrigger
        v-if="isShowMenu && ((getShowContent && getShowHeaderTrigger && !getSplit && !getIsMixSidebar) || getIsMobile)"
        :theme="getHeaderTheme"
        :sider="false"
      />
      <LayoutBreadcrumb v-if="getShowContent && getShowBread" :theme="getHeaderTheme" />
      <!-- 欢迎语 -->
      <span
        v-if="getShowContent && getShowBreadTitle && !getIsMobile"
        :class="[prefixCls, `${prefixCls}--${getHeaderTheme}`, 'headerIntroductionClass']"
      >
        {{ t('common.welcomeGj') }} {{ `${userInfo.username || ''} ${userInfo.roleName || ''} ${userInfo.currentZone || ''}` }}
        <!-- todo -->
      </span>

      <div class="change_store" v-if="isClueOrOrder && storeInfo.networkList.length > 0">
        <span class="store_text">{{ t('common.changStore') }}</span>
        <a-select class="store_select" v-model:value="storeInfo.networkCode" @change="storeChange" allowClear>
          <template v-for="item in storeInfo.networkList" :key="`${item.networkCode}`">
            <a-select-option :value="item.networkCode">{{ item.networkName }}</a-select-option>
          </template>
        </a-select>
      </div>
    </div>
    <!-- left end -->

    <!-- menu start -->
    <div :class="`${prefixCls}-menu`" v-if="getShowTopMenu && !getIsMobile">
      <LayoutMenu :isHorizontal="true" :theme="getHeaderTheme" :splitType="getSplitType" :menuMode="getMenuMode" />
    </div>
    <!-- menu-end -->

    <!-- action  -->
    <div :class="`${prefixCls}-action`">
      <a-button type="link" @click="viewAPIDocumentation">API Documentation</a-button>

      <a-button type="link" @click="viewAcordeDePrivacidade">{{ t('common.privacy_agreement_title') }}</a-button>

      <!-- <AppSearch :class="`${prefixCls}-action__item `" v-if="getShowSearch" /> -->

      <ErrorAction v-if="getUseErrorHandle" :class="`${prefixCls}-action__item error-action`" />

      <Notify v-if="getShowNotice" :class="`${prefixCls}-action__item notify-item`" />

      <FullScreen v-if="getShowFullScreen" :class="`${prefixCls}-action__item fullscreen-item`" />

      <LockScreen v-if="getUseLockPage" />

      <AppLocalePicker v-if="getShowLocalePicker" :reload="true" :showText="true" :class="`${prefixCls}-action__item`" />

      <UserDropDown :theme="getHeaderTheme" />

      <SettingDrawer v-if="getShowSetting" :class="`${prefixCls}-action__item`" />
    </div>
  </Header>
  <!-- 隐私协议 -->
  <PrivacyAgreementModal @register="privacyAgreementsModal" />
</template>
<script lang="ts">
  import { defineComponent, unref, computed, watch, onMounted, reactive, ref } from 'vue';
  import { useRoute } from 'vue-router';
  import { awaitTo } from '@ruqi/utils-admin';
  import { useGlobSetting } from '/@/hooks/setting';
  import { propTypes } from '/@/utils/propTypes';

  import { Layout } from 'ant-design-vue';
  import { AppLogo } from '/@/components/Application';
  import LayoutMenu from '../menu/index.vue';
  import LayoutTrigger from '../trigger/index.vue';

  import { PageEnum } from '/@/enums/pageEnum';
  import { useRouter } from 'vue-router';
  import { AppSearch } from '/@/components/Application';
  import { useModal } from '/@/components/Modal';
  import { useHeaderSetting } from '/@/hooks/setting/useHeaderSetting';
  import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
  import { useRootSetting } from '/@/hooks/setting/useRootSetting';
  import { getUserInfo } from '/@/api/sys/user';

  import { MenuModeEnum, MenuSplitTyeEnum } from '/@/enums/menuEnum';
  import { SettingButtonPositionEnum } from '/@/enums/appEnum';
  import { AppLocalePicker } from '/@/components/Application';
  import PrivacyAgreementModal from './components/privacyAgreementModal.vue';

  import { UserDropDown, LayoutBreadcrumb, FullScreen, Notify, ErrorAction, LockScreen } from './components';
  import { useAppInject } from '/@/hooks/web/useAppInject';
  import { useDesign } from '/@/hooks/web/useDesign';

  import { createAsyncComponent } from '/@/utils/factory/createAsyncComponent';
  import { useLocale } from '/@/locales/useLocale';
  import { useI18n } from '/@/hooks/web/useI18n';

  import { useUserStore } from '/@/store/modules/user';
  import { featchStoreList, switchStore, StoreInfo } from '/@/api/sys/user';

  const { t } = useI18n();

  export default defineComponent({
    name: 'LayoutHeader',
    components: {
      Header: Layout.Header,
      AppLogo,
      LayoutTrigger,
      LayoutBreadcrumb,
      LayoutMenu,
      UserDropDown,
      AppLocalePicker,
      FullScreen,
      Notify,
      AppSearch,
      ErrorAction,
      LockScreen,
      SettingDrawer: createAsyncComponent(() => import('/@/layouts/default/setting/index.vue'), {
        loading: true,
      }),
      PrivacyAgreementModal,
    },
    props: {
      fixed: propTypes.bool,
      isShowMenu: {
        type: Boolean,
        default: true,
      },
    },
    setup(props) {
      const { prefixCls } = useDesign('layout-header');
      const { getShowTopMenu, getShowHeaderTrigger, getSplit, getIsMixMode, getMenuWidth, getIsMixSidebar } = useMenuSetting();
      const { getUseErrorHandle, getShowSettingButton, getSettingButtonPosition } = useRootSetting();
      const { title } = useGlobSetting();
      const userInfo = ref({});

      const router = useRouter();
      const route = useRoute();
      // 切换门店相关参数
      const storeInfo: StoreInfo = reactive({
        networkList: [],
        networkCode: '',
        accountCategory: '',
      });
      const isClueOrOrder = ref(route.meta?.pageType === 'clueOrOrder');

      // 隐私协议
      const [privacyAgreementsModal, { openModal: openPrivacyAgreementsModal }] = useModal();

      const {
        getHeaderTheme,
        getShowFullScreen,
        getShowNotice,
        getShowContent,
        getShowBread,
        getShowHeaderLogo,
        getShowHeader,
        getShowSearch,
        getUseLockPage,
        getShowBreadTitle,
      } = useHeaderSetting();

      const { getShowLocalePicker } = useLocale();

      const { getIsMobile } = useAppInject();

      const getHeaderClass = computed(() => {
        const theme = unref(getHeaderTheme);
        return [
          prefixCls,
          {
            [`${prefixCls}--fixed`]: props.fixed,
            [`${prefixCls}--mobile`]: unref(getIsMobile),
            [`${prefixCls}--${theme}`]: theme,
          },
        ];
      });

      const getShowSetting = computed(() => {
        if (!unref(getShowSettingButton)) {
          return false;
        }
        const settingButtonPosition = unref(getSettingButtonPosition);

        if (settingButtonPosition === SettingButtonPositionEnum.AUTO) {
          return unref(getShowHeader);
        }
        return settingButtonPosition === SettingButtonPositionEnum.HEADER;
      });

      const getLogoWidth = computed(() => {
        if (!unref(getIsMixMode) || unref(getIsMobile)) {
          return {};
        }
        const width = unref(getMenuWidth) < 180 ? 180 : unref(getMenuWidth);
        return { width: `${width}px` };
      });

      const getSplitType = computed(() => {
        return unref(getSplit) ? MenuSplitTyeEnum.TOP : MenuSplitTyeEnum.NONE;
      });

      const getMenuMode = computed(() => {
        return unref(getSplit) ? MenuModeEnum.HORIZONTAL : null;
      });

      // 查看隐私协议
      const viewAcordeDePrivacidade = () => {
        openPrivacyAgreementsModal(true, {});
      };

      // 查看API文档
      const viewAPIDocumentation = () => {
        window.open(router.resolve({ path: PageEnum.API_DOCUMENT }).href, '_blank');
      };

      // 获取用户信息
      const getUserInfoData = async () => {
        const userRes: any = await getUserInfo();
        useUserStore().getUserInfoAction();
        userInfo.value = userRes || {};
      };

      onMounted(() => {
        getUserInfoData();
      });

      const storeChange = (e) => {
        if (!e) return;
        switchStore({ networkCode: e }).then(() => {
          location.reload();
        });
      };

      // 初始化加载门店信息
      const loadStoreData = async () => {
        const [, res] = await awaitTo(featchStoreList());
        const { networkCode, networkList = [], accountCategory = '' } = res || {};
        if (networkList.length === 0 || !['2', '3'].includes(accountCategory)) return;
        // 监听路由变化
        watch(
          () => route.meta?.pageType,
          (newPageType) => {
            isClueOrOrder.value = newPageType === 'clueOrOrder';
          },
          { immediate: true }
        );
        storeInfo.networkCode = networkCode || networkList[0].networkCode;
        storeInfo.networkList = networkList.filter((item: any) => item.type === accountCategory);
      };
      loadStoreData();

      return {
        prefixCls,
        getHeaderClass,
        getShowHeaderLogo,
        getHeaderTheme,
        getShowHeaderTrigger,
        getIsMobile,
        getShowBreadTitle,
        getShowBread,
        getShowContent,
        getSplitType,
        getSplit,
        getMenuMode,
        getShowTopMenu,
        getShowLocalePicker,
        getShowFullScreen,
        getShowNotice,
        getUseErrorHandle,
        getLogoWidth,
        getIsMixSidebar,
        getShowSettingButton,
        getShowSetting,
        getShowSearch,
        viewAcordeDePrivacidade,
        viewAPIDocumentation,
        privacyAgreementsModal,
        getUseLockPage,
        title,
        t,
        userInfo,
        isClueOrOrder,
        storeInfo,
        storeChange,
      };
    },
  });
</script>
<style lang="less">
  @import './index.less';
  //update-begin---author:scott ---date:2022-09-30  for：默认隐藏顶部菜单面包屑-----------
  //顶部欢迎语展示样式
  @prefix-cls: ~'@{namespace}-layout-header';

  .ant-layout .@{prefix-cls} {
    display: flex;
    padding: 0 8px;
    // update-begin--author:liaozhiyang---date:20240407---for：【QQYUN-8762】顶栏高度
    height: @header-height;
    // update-end--author:liaozhiyang---date:20240407---for：【QQYUN-8762】顶栏高度
    align-items: center;

    .headerIntroductionClass {
      margin-right: 4px;
      margin-bottom: 2px;
      border-bottom: 0px;
      border-left: 0px;
      line-height: normal;
    }

    &--light {
      .headerIntroductionClass {
        color: #000;
      }
    }

    &--dark {
      .headerIntroductionClass {
        color: rgba(255, 255, 255, 1);
      }
      .anticon,
      .truncate {
        color: rgba(255, 255, 255, 1);
      }
    }
    //update-end---author:scott ---date::2022-09-30  for：默认隐藏顶部菜单面包屑--------------
  }
  .change_store {
    display: flex;
    align-items: center;
  }
  .store_text {
    color: #1990ff;
    margin: 0 10px 0 50px;
    line-height: 16px;
  }
  .store_select {
    min-width: 100px;
  }
</style>
