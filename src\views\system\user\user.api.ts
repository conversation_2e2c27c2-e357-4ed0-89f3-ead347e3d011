import { defHttp } from '/@/utils/http/axios';
import { Modal } from 'ant-design-vue';
import { useI18n } from '/@/hooks/web/useI18n';

const { t } = useI18n();

/**
 * 账户列表接口(查询用户，通过租户隔离)
 * @param params
 */
export const accountListApi = (params) => defHttp.post({ url: '/sys/user/list', params });

/**
 * 绑定角色
 * @param params
 */
export const bindRolesApi = (params) => defHttp.post({ url: '/sys/role/roleAssignPermissions', data: params });

/**
 * 初始化账号
 * @param userIds:[id]
 */
export const initUserApi = (params) => defHttp.post({ url: '/sys/user/initUser', data: params });

/**
 * 查询用户关联的国家信息
 * @param params
 */
export const queryCountryByUserApi = (params) => defHttp.post({ url: `/sys/user/country/queryCountryByUser?userId=${params.userId}` });

/**
 * 为用户关联绑定国家信息
 * @param params
 */
export const userBindCountryApi = (params) => defHttp.post({ url: '/sys/user/country/userBindCountry', data: params });

/**
 * 导出api
 * @param params
 */
export const getExportUrl = '/sys/user/exportUserList';

/**
 * 导入api
 */
export const getImportApi = (params, handleSuccess) =>
  defHttp.post({ url: '/sys/user/batchImportUser', data: params }).then((res) => {
    handleSuccess(res);
  });

/**
 * 导出文件地址-获取下载地址的
 */
export const queryExportUrlApi = () => defHttp.post({ url: '/sys/user/queryExportUrl' });

/**
 * 删除用户
 */
export const deleteUserApi = (params, handleSuccess) => {
  Modal.confirm({
    title: t('common.okTextDelete'),
    content: `${t('common.del_sys_user_confirm')}`,
    okText: t('common.okText'),
    cancelText: t('common.cancelText'),
    onOk: () => {
      return defHttp.post({ url: `/sys/user/delete?userId=${params.userId}`, params }).then(() => {
        handleSuccess();
      });
    },
  });
};

/**
 * 更新账户-禁用/启用状态
 * @param params
 */
export const updateAccountStatusApi = (params: {
  changeReason: string;
  status: number; // 1:启用 2:禁用
  userId: string;
}) => defHttp.post({ url: '/sys/user/updateUserStatus', data: params });

/**
 * 获取账户信息
 * @param params
 */
export const queryUserInfoApi = (params: { userId: string }) => defHttp.post({ url: `/sys/user/queryUserInfo?userId=${params.userId}` });

/**
 * 保存账户信息
 * @param params
 */
export const saveUserInfoApi = (params) => defHttp.post({ url: '/sys/user/save', data: params });

/**
 * 更新账户信息
 * @param params
 */
export const updateUserInfoApi = (params) => defHttp.post({ url: '/sys/user/update', data: params });
