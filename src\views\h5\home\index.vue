<template>
  <div class="box_container">
    <div class="h5_head">
      <img :src="logoImg" />
      <span>{{ t('welcomeGj') }}</span>
    </div>
    <div class="create_account_box">
      <span class="create_account_box_title">{{ t('create_account') }}</span>
      <span class="create_account_box_desc">{{ t('create_account_desc') }}</span>
    </div>
    <div class="create_account_field">
      <div class="create_account_field_item">
        <div class="label">{{ t('please_select_network_text') }}</div>
        <div class="plase_select" @click="showNetpoint = true">
          <span>{{ t('chooseText') }}</span>
          <Icon name="arrow" />
        </div>
      </div>
      <div v-if="selectEndNetpointData1.length > 0 || selectEndNetpointData2.length > 0" class="flex" style="margin-top: 10px">
        <div
          class="create_account_field_item"
          style="flex-wrap: wrap; margin-right: 10px; margin-bottom: 10px"
          v-for="(item, index) in selectEndNetpointData1"
          :key="index"
        >
          <Tag :plain="false" :round="true" size="large" color="#1990ff">{{ item.label }}</Tag>
        </div>
        <div
          class="create_account_field_item"
          style="flex-wrap: wrap; margin-right: 10px; margin-bottom: 10px"
          v-for="(item, index) in selectEndNetpointData2"
          :key="index"
        >
          <Tag :plain="false" :round="true" size="large" color="#1990ff">{{ item.label }}</Tag>
        </div>
      </div>
    </div>
    <div class="create_account_field">
      <div class="create_account_field_item">
        <div class="label">{{ t('selectRole') }}</div>
        <div class="plase_select" @click="showRole = true">
          <span>{{ t('chooseText') }}</span>
          <Icon name="arrow" />
        </div>
      </div>
      <div v-if="selectEndRoleData.length > 0" class="flex" style="flex-wrap: wrap; margin-top: 10px">
        <div
          class="create_account_field_item"
          style="margin-bottom: 10px; margin-right: 10px"
          v-for="(item, index) in selectEndRoleData"
          :key="index"
        >
          <Tag :plain="false" :round="true" size="large" color="#1990ff">{{ item.label }}</Tag>
        </div>
      </div>
    </div>

    <Button style="margin-top: 56px" type="primary" @click="handleCreateAccount()">{{ t('createInviteLink') }}</Button>
  </div>
  <!-- <tabbar /> -->

  <!-- 选择网点 -->
  <Popup
    :show="showNetpoint"
    closeable
    position="bottom"
    :style="{ height: '68%' }"
    @click-overlay="showNetpoint = false"
    @close="showNetpoint = false"
  >
    <div class="popup_select_box">
      <div class="popup_select_head">
        <div class="popop_select_head_left">
          <text>{{ t('please_select_network_text') }}</text>
          <text>({{ t('multi_select') }})</text>
        </div>
        <div style="color: #1990ff" @click="handleWangdianConfirm">{{ t('okText') }}</div>
      </div>
      <div class="popup_select_content" style="display: flex; flex-direction: column; gap: 10px">
        <div class="flex" style="flex-wrap: wrap" v-if="selectNetpointData1.length > 0 && accountCategory == '2'">
          <span style="margin-right: 10px">{{ t('dealerCustomerText') }}</span>
          <div class="popup_select_content_item" v-for="(item, index) in selectNetpointData1" :key="index">
            <Tag :plain="!item.isSelect" :round="true" size="large" color="#1990ff" @click="item.isSelect = !item.isSelect">{{ item.label }}</Tag>
          </div>
        </div>
        <div class="flex" style="flex-wrap: wrap" v-if="selectNetpointData2.length > 0 && accountCategory == '3'">
          <span style="margin-right: 10px">{{ t('network_text') }}</span>
          <div class="popup_select_content_item" v-for="(item, index) in selectNetpointData2" :key="index">
            <Tag :plain="!item.isSelect" :round="true" size="large" color="#1990ff" @click="item.isSelect = !item.isSelect">{{ item.label }}</Tag>
          </div>
        </div>
      </div>
    </div>
  </Popup>

  <!-- 选择角色 -->
  <Popup :show="showRole" closeable position="bottom" :style="{ height: '68%' }" @click-overlay="showRole = false" @close="showRole = false">
    <div class="popup_select_box">
      <div class="popup_select_head">
        <div class="popup_select_head_left">
          <text>{{ t('selectRole') }}</text>
          <text>({{ t('multi_select') }})</text>
        </div>
        <div style="color: #1990ff" @click="handleRoleConfirm">{{ t('okText') }}</div>
      </div>
      <div class="popup_select_content" style="display: flex; flex-direction: column; gap: 10px">
        <div class="flex" style="flex-wrap: wrap" v-if="selectRoleData.length > 0">
          <span></span>
          <div class="popup_select_content_item" v-for="(item, index) in selectRoleData" :key="index">
            <Tag :plain="!item.isSelect" :round="true" size="large" color="#1990ff" @click="item.isSelect = !item.isSelect">{{ item.label }}</Tag>
          </div>
        </div>
      </div>
    </div>
  </Popup>

  <!-- 生成邀请链接成功 -->
  <Popup
    :show="showCreateInviteLinkSuccess"
    @close="showCreateInviteLinkSuccess = false"
    :closeable="true"
    position="center"
    :style="{ width: '86%', minHeight: '50%' }"
  >
    <div class="popup_select_box">
      <div class="popup_select_head">
        <div class="popop_select_head_left">
          <text>{{ t('inviteLinkCreatedSuccess') }}</text>
        </div>
      </div>
      <div style="display: flex; flex-direction: column; padding: 0 16px; margin-top: 16px">
        <div>
          <span>{{ t('loginAddress') }}：</span>
          <span>{{ createInviteLinkSuccessInfo.loginUrl }}</span>
        </div>
        <div>
          <span>{{ t('authAccount') }}：</span>
          <span>{{ createInviteLinkSuccessInfo.account }}</span>
        </div>
        <div>
          <span>{{ t('authPassword') }}：</span>
          <span>{{ createInviteLinkSuccessInfo.password }}</span>
        </div>
        <!-- 7天后 -->
        <div>{{ t('thisAuthInfoIsEffectiveFor7Days') }}：{{ createInviteLinkSuccessInfo.endTime }}</div>
      </div>
      <div style="padding: 0 16px 16px; margin-top: 20px">
        <Button type="primary" @click="handleCopyInviteLink()">{{ t('copyInviteLink') }}</Button>
        <div style="margin-top: 16px; color: #666666">{{ t('sendLinkToCorrespondingPositions') }}</div>
      </div>
    </div>
  </Popup>
</template>
<script lang="ts" setup name="h5-home-test">
  import { ref, onMounted, computed } from 'vue';
  import dayjs from 'dayjs';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useUserStore } from '/@/store/modules/user';
  import logoImg from '/@/assets/images/logo.png';
  import { showToast, Popup, Button, Tag, Icon } from 'vant';
  import 'vant/lib/index.css';
  import { createInviteLinkApi, querListByUserNetworkApi, getStoreRoleListApi } from '/@/api/h5Api/loginXiangguan';

  const { t } = useI18n('common');

  const userStore = useUserStore();

  /**
   * 账户类别 1厂端、2总代与子公司、3网点
   * */
  const accountCategory = computed(() => {
    // @ts-ignore
    return userStore.getUserInfo?.accountCategory;
  });

  // 选择网点弹窗是否显示
  const showNetpoint = ref(false);
  // 选中的网点数据
  const selectEndNetpointData1 = ref([] as any[]);
  const selectEndNetpointData2 = ref([] as any[]);
  // 选择网点弹窗数据
  const selectNetpointData1 = ref([] as any[]);
  const selectNetpointData2 = ref([] as any[]);
  // 选择网点弹窗确定
  const handleWangdianConfirm = () => {
    const selectData = [...selectNetpointData1.value, ...selectNetpointData2.value].filter((filterItems) => filterItems.isSelect);
    if (selectData.length <= 0) {
      showToast(t('please_select_network_text'));
      return;
    }
    selectEndNetpointData1.value = selectNetpointData1.value.filter((filterItems) => filterItems.isSelect);
    selectEndNetpointData2.value = selectNetpointData2.value.filter((filterItems) => filterItems.isSelect);
    // 清空角色选中的数据
    selectEndRoleData.value = [];
    /**
     * 重置角色下拉数据-依照initRoleData数据对筛选对比
     * 三种情况
     * 1：是否包含type=2和3的
     * 2: 如果都包含则是全量数据，都不包含空数据
     * 2:只包含一个按照type类型筛选
     * */
    // 都包含
    if (selectEndNetpointData1.value.length > 0 && selectEndNetpointData2.value.length > 0) {
      selectRoleData.value = initRoleData.value;
    } else if (selectEndNetpointData1.value.length > 0 || selectEndNetpointData2.value.length > 0) {
      // 只包含一个按照type类型筛选
      selectRoleData.value = initRoleData.value.filter((filterItems) => {
        return filterItems.type == (selectEndNetpointData1.value.length > 0 ? 2 : 3);
      });
      // 执行对selectRoleData.value每条对象中的roleId相同的进行去重
      const uniqueRoleMap = new Map();
      selectRoleData.value = selectRoleData.value.filter((item) => {
        if (!uniqueRoleMap.has(item.value)) {
          uniqueRoleMap.set(item.value, true);
          return true;
        }
        return false;
      });
    } else {
      // 都不包含
      selectRoleData.value = [];
    }
    showNetpoint.value = false;
  };

  // 选择角色弹窗是否显示
  const showRole = ref(false);
  // 选中的角色数据
  const selectEndRoleData = ref([] as any[]);
  // 初始化得到的数据
  const initRoleData = ref([] as any[]);
  // 选择角色弹窗数据
  const selectRoleData = ref([] as any[]);
  // 选择角色弹窗确定
  const handleRoleConfirm = () => {
    const selectData = selectRoleData.value.filter((filterItems) => filterItems.isSelect);
    if (selectData.length <= 0) {
      showToast(t('pleaseSelectSameRole'));
      return;
    }
    selectEndRoleData.value = selectData;
    showRole.value = false;
  };

  // 创建邀请链接成功
  const showCreateInviteLinkSuccess = ref(false);

  // 创建邀请链接-loading
  const createInviteLinkLoading = ref(false);

  // 创建邀请链接成功信息
  const createInviteLinkSuccessInfo = ref({
    loginUrl: `${window.location.origin}/mobile/h5Login`,
    account: '',
    password: '',
    endTime: '',
  });

  // 生成邀请链接
  const handleCreateAccount = async () => {
    if (selectEndNetpointData1.value.length <= 0 && selectEndNetpointData2.value.length <= 0) {
      showToast(t('please_select_network_text'));
      return;
    }
    if (selectEndRoleData.value.length <= 0) {
      showToast(t('pleaseSelectSameRole'));
      return;
    }
    if (createInviteLinkLoading.value) return;
    createInviteLinkLoading.value = true;
    try {
      const res = await createInviteLinkApi({
        regionCode: userStore.getUserInfo.regionCode,
        countryCode: userStore.getUserInfo.country,
        role: selectEndRoleData.value.map((mapItems) => mapItems.value).join(','),
        customerCode: selectEndNetpointData1.value.map((mapItems) => mapItems.value).join(','),
        networkCode: selectEndNetpointData2.value.map((mapItems) => mapItems.value).join(','),
      });
      if (!res) {
        showToast(t('createInviteLinkFailed'));
        return;
      }
      createInviteLinkSuccessInfo.value = {
        ...createInviteLinkSuccessInfo.value,
        account: res.userCode,
        password: res.authPwd,
        endTime: dayjs().add(7, 'day').format('YYYY-MM-DD HH:mm'),
      };
      showCreateInviteLinkSuccess.value = true;
    } catch (err) {
    } finally {
      createInviteLinkLoading.value = false;
    }
  };

  // 复制邀请链接
  const handleCopyInviteLink = () => {
    const info = createInviteLinkSuccessInfo.value;
    const text = `${t('loginAddress')}：\n${info.loginUrl}\n\n${t('authAccount')}：\n${info.account}\n\n${t('authPassword')}：\n${info.password}\n\n${t('thisAuthInfoIsEffectiveFor7Days')}：${info.endTime}`;
    navigator.clipboard.writeText(text);
    showToast(t('copySuccess'));
  };

  onMounted(() => {
    querListByUserNetworkApi({ userId: userStore.getUserInfo.userId }).then((res) => {
      const type1Arr = res?.filter((item) => item.type === 2) || [];
      const type2Arr = res?.filter((item) => item.type === 3) || [];
      selectNetpointData1.value = type1Arr.map((mapItems) => {
        return {
          label: mapItems.networkName,
          value: mapItems.networkCode,
          isSelect: false,
        };
      });
      selectNetpointData2.value = type2Arr.map((mapItems) => {
        return {
          label: mapItems.networkName,
          value: mapItems.networkCode,
          isSelect: false,
        };
      });
    });
    getStoreRoleListApi().then((res) => {
      for (let i in res) {
        (res[i] || []).forEach((forItems) => {
          initRoleData.value.push({
            label: forItems.roleName,
            value: forItems.roleId,
            type: i,
            isSelect: false,
          });
        });
      }
    });
  });
</script>
<style scoped lang="less">
  // .common-page {
  //   width: 100%;
  //   min-height: 100vh;
  //   padding: 20px;
  //   box-sizing: border-box;
  //   background: #ffffff;
  // }
  .h5_head {
    display: flex;
    align-items: center;
    img {
      width: 60px;
    }
    span {
      margin-left: 10px;
      font-size: 16px;
      font-weight: bolder;
    }
  }
  .create_account_box {
    margin-top: 26px;
    display: flex;
    flex-direction: column;
    .create_account_box_title {
      font-size: 22px;
      font-weight: bolder;
      color: #000000;
    }
    .create_account_box_desc {
      font-size: 14px;
      color: #666666;
    }
  }

  .create_account_field {
    margin-top: 28px;
    .create_account_field_item {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .label {
      color: #000000;
      &::before {
        display: inline-block;
        content: '*';
        color: #ff0000;
      }
    }
    .plase_select {
      color: #666666;
    }
  }

  ::v-deep(.van-popup__close-icon--top-right) {
    top: 10px;
  }
  .popup_select_box {
    width: 100%;
    height: 100%;
    .popup_select_head {
      width: 100%;
      min-height: 40px;
      background: #ffffff;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 15px 50px 10px 16px;
      box-sizing: border-box;
      color: #000000;
      font-weight: bolder;
      .popup_select_head_left {
        line-height: 20px;
      }
    }
    .popup_select_content {
      display: flex;
      flex-wrap: wrap;
      width: 100%;
      height: calc(100% - 40px);
      overflow: auto;
      padding: 16px;
      box-sizing: border-box;
      .popup_select_content_item {
        margin-right: 10px;
        margin-bottom: 10px;
      }
    }
  }
  ::v-deep(.van-button) {
    width: 100%;
  }
</style>
