<template>
  <page-wrapper>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <TableAction :actions="renderTableTitleActions()" />
      </template>
      <template #action="{ record }">
        <TableAction :actions="renderTableActions(record)" />
      </template>
    </BasicTable>
    <HandleModal @register="registerModal" @success="reload" :nation-options="nationOptions" />
  </page-wrapper>
</template>

<script lang="ts" setup>
  import { onMounted } from 'vue';
  import { awaitTo } from '@ruqi/utils-admin/esm/tools';
  import { getLabelFromDict } from '@ruqi/utils-admin';
  import PageWrapper from '/@/components/Page/src/PageWrapper.vue';
  import { ActionItem, BasicColumn, BasicTable, FormSchema, TableAction } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useNationOptions } from '/@/hooks/common';
  import { AuthEnum } from '/@/enums/authEnum';
  import { CancelConfigTypeOptions } from '@/enums/storeEnum';
  import { featchCancelAudit, featchCancelAuditDelete } from '@/api/sys/store';
  import HandleModal from './handleModal.vue';

  const t = useI18n('common').t;
  const { createConfirm, createMessage } = useMessage();
  const [registerModal, { openModal }] = useModal();

  const columns: BasicColumn[] = [
    {
      title: t('serialNumber'),
      dataIndex: 'id',
      width: 100,
      resizable: true,
    },
    {
      title: t('countryText'),
      dataIndex: 'countryName',
      width: 150,
      resizable: true,
    },
    {
      title: t('errorLog_tableColumnType'),
      dataIndex: 'type',
      width: 150,
      resizable: true,
      customRender: ({ record }) => {
        return getLabelFromDict(record.type, CancelConfigTypeOptions);
      },
    },
    {
      title: t('cancelAuditTime'),
      dataIndex: 'standardValue',
      width: 200,
      resizable: true,
    },
    {
      title: t('operator'),
      dataIndex: 'operator',
      width: 150,
      resizable: true,
    },
    {
      title: t('operationTime'),
      dataIndex: 'operatingTime',
      width: 150,
      resizable: true,
    },
  ];

  const { nationOptions, getData } = useNationOptions({
    enableSearchCity: true,
  });

  const searchFormSchema: FormSchema[] = [
    {
      label: t('countryText'),
      field: 'countryCode',
      component: 'Select',
      labelWidth: '100%',
      componentProps: {
        placeholder: t('chooseText'),
        options: nationOptions,
        showSearch: true,
      },
    },
  ];

  const { tableContext } = useListPage({
    designScope: 'SSACancelAuditConfig',
    tableProps: {
      api: featchCancelAudit,
      columns,
      formConfig: {
        schemas: searchFormSchema,
        buttonLayout: 'right',
        layout: 'vertical',
      },
      useSearchForm: true,
      showTableSetting: false,
      showActionColumn: true,
      actionColumn: {
        width: 150,
        title: t('operation'),
      },
    },
  });

  const renderTableActions = (record: Recordable): ActionItem[] => {
    return [
      {
        label: t('edit'),
        onClick: () => {
          openModal(true, record);
        },
        auth: AuthEnum.SSA_CANCEL_AUDIT_CONFIG_EDIT,
      },
      {
        label: t('delText'),
        color: 'error',
        onClick: async () => {
          createConfirm({
            title: t('okTextDelete'),
            iconType: 'warning',
            content: `${t('deleteTip')}`,
            okText: t('okText'),
            cancelText: t('cancelText'),
            onOk: async () => {
              const [err] = await awaitTo(featchCancelAuditDelete(record.id));
              if (!err) {
                createMessage.success(t('operationSuccess'));
                reload();
              }
            },
          });
        },
        auth: AuthEnum.SSA_CANCEL_AUDIT_CONFIG_DELETE,
        ifShow: () => record.type !== 0,
      },
    ];
  };
  const [registerTable, { reload }] = tableContext;

  const renderTableTitleActions = (): ActionItem[] => {
    return [
      {
        label: t('add'),
        size: 'middle',
        onClick: () => {
          openModal(true, { type: 1 });
        },
        auth: AuthEnum.SSA_CANCEL_AUDIT_CONFIG_ADD,
        type: 'primary',
      },
    ];
  };

  // 更新 options
  onMounted(async () => {
    await awaitTo(getData());
  });
</script>

<style lang="scss" scoped></style>
