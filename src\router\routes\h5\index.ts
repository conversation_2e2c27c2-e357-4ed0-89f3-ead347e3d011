import type { AppRouteRecordRaw } from '/@/router/types';

import { useI18n } from '/@/hooks/web/useI18n';

const { t } = useI18n('common');

// H5登录
export const h5Login: AppRouteRecordRaw = {
  path: '/mobile/h5Login',
  name: 'h5Login',
  component: () => import('/@/views/h5/login/index.vue'),
  meta: {
    title: t('loginButton'),
  },
};

// H5 WORD预览
export const h5DocOnline: AppRouteRecordRaw = {
  path: '/mobile/h5DocOnline',
  name: 'h5DocOnline',
  component: () => import('/@/views/h5/docOnline/index.vue'),
  meta: {
    title: t('loginButton'),
  },
};

// 取消订阅
export const h5CancelarAssinatura: AppRouteRecordRaw = {
  path: '/mobile/h5CancelarAssinatura',
  name: 'h5CancelarAssinatura',
  component: () => import('/@/views/h5/cancelarAssinatura/index.vue'),
  meta: {
    title: t('common.cancel_dingyue_text'),
  },
};

// H5忘记密码
export const h5ForgetPassword: AppRouteRecordRaw = {
  path: '/mobile/h5ForgetPassword',
  name: 'h5ForgetPassword',
  component: () => import('/@/views/h5/forgetPassword/index.vue'),
  meta: {
    title: t('activate_account_text'),
  },
};

export const h5BasicRoutes = [h5CancelarAssinatura, h5Login, h5DocOnline, h5ForgetPassword];
