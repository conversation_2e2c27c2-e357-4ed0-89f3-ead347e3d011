import { defHttp } from '/@/utils/http/axios';

/**
 * 获取取消订阅的值
 * @data encryptOneId-oneId
 */
export const getCancelDingyueApi = (data) => {
  return defHttp.post({
    url: `/v1/querySubscription?encryptOneId=${data.encryptOneId}`,
  });
};

/**
 * 更新用户更新订阅状态
 * @data encryptOneId-oneId
 * @data websiteSubscriptionEmail-订阅邮箱
 * @data websiteSubscriptionPhone-订阅手机
 * @data subscriptionSms-订阅短信
 * @data subscriptionWhatsApp-订阅WhatsApp
 * @data subscriptionLine-订阅line
 * @data subscriptionVehicle-订阅车机
 * @data subscriptionAppMsg-订阅APP-站内信
 */
export const userUpdateSubscriptionApi = (data) => {
  return defHttp.post({ url: '/v1/userUpdateSubscription', data });
};
